import uuid
from storage.minio import MinioStorage
from rest_framework.exceptions import APIException


def upload_file_to_minio(file_obj):
    try:
        fid = uuid.uuid4()
        object_name = f"{fid}/{file_obj.name.lower()}"
        minio = MinioStorage()
        minio.upload(
            bucket_name="private",
            object_name=object_name,
            data=file_obj,
            length=file_obj.size,
            content_type=file_obj.content_type,
        )

        return fid, object_name
    except Exception as e:
        raise APIException(e)
