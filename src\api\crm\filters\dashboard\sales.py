"""
Sales Dashboard Filters for CRM
Provides filtering capabilities for sales dashboard analytics
"""

from django_filters import rest_framework as filters
from django.db.models import Q
from core.models import Order, Offering, User


class CrmDashboardSalesFilter(filters.FilterSet):
    """
    Filter for Sales Dashboard
    Supports filtering by date ranges, stages, products, and sales agents
    """

    # Date range filters
    created_at = filters.DateFromToRangeFilter()

    # Stage filters - comma separated list
    stages = filters.CharFilter(
        method="filter_stages",
        help_text="Comma-separated list of order stages (prospect,interested,to_pay,sold,lost)",
    )

    # Products filters - comma separated list of offering IDs
    products = filters.CharFilter(
        method="filter_products", help_text="Comma-separated list of offering IDs"
    )

    # Sales agent filter
    sales_agent = filters.CharFilter(
        field_name="sales_agent__uid",
        lookup_expr="exact",
        help_text="Sales agent UID for filtering",
    )

    class Meta:
        model = Order
        fields = ["created_at", "stages", "products", "sales_agent"]

    def filter_stages(self, queryset, name, value):
        """
        Filter by multiple stages (comma-separated)
        """
        if not value:
            return queryset

        stages = [stage.strip() for stage in value.split(",") if stage.strip()]
        if stages:
            return queryset.filter(stage__in=stages)
        return queryset

    def filter_products(self, queryset, name, value):
        """
        Filter by multiple products/offerings (comma-separated)
        """
        if not value:
            return queryset

        product_ids = [pid.strip() for pid in value.split(",") if pid.strip()]
        if product_ids:
            return queryset.filter(items__offering__oid__in=product_ids).distinct()
        return queryset
