from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from core.models import EventReminder
from api.crm.serializers.event_reminder import (
    CrmEventReminderSerializer,
    CrmCreateEventReminderSerializer,
    CrmSendEventReminderSerializer,
    CrmUpdateEventReminderSerializer,
)
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from api.mixins import AuditMixin, SwaggerTagMixin


class CrmEventReminderViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = EventReminder
    queryset = EventReminder.objects.filter(deleted=False).order_by("created_at")
    serializer_class = CrmEventReminderSerializer
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsAdminUser]

    swagger_tags = ["EventReminders"]

    def get_serializer_class(self):
        if self.action == "create":
            return CrmCreateEventReminderSerializer
        if self.action in ["update", "partial_update"]:
            return CrmUpdateEventReminderSerializer
        return super().get_serializer_class()
        
    @action(detail=True, methods=["post"], url_path="send")
    def send_event_reminder(self, request, pk=None):
        """
        Acción personalizada para enviar un recordatorio de evento a WhatsApp.
        """
        event_reminder = self.get_object()
        serializer = CrmSendEventReminderSerializer(event_reminder)
        try:
            res = serializer.send_event_reminder(event_reminder)
            if res.get("errors"):
                return Response(
                    {
                        "detail": "Recordatorio enviado a WhatsApp con algunos errores.",
                        "errors": res.get("errors"),
                        "count": res.get("count"),
                    }
                )
            return Response(
                {"detail": "Recordatorio enviado a WhatsApp exitosamente.", "count": res.get("count")}
            )
        except Exception as e:
            return Response(
                {"detail": f"{str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def destroy(self, request, *args, **kwargs):
        """
        Marca un recordatorio como eliminada en lugar de eliminarla físicamente.
        """
        instance = self.get_object()
        instance.deleted = True
        instance.save()

        return Response(
            {"detail": "El recordatorio se marcó como eliminado."},
            status=status.HTTP_204_NO_CONTENT,
        )