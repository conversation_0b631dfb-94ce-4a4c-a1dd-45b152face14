import io
import uuid
from django.conf import settings
from core.models import File
from typing import TypedDict
from PIL import Image
from storage.minio import MinioStorage
from rest_framework.exceptions import APIException

PROFILE_PHOTO_WIDTH = 120
PROFILE_PHOTO_HEIGHT = 120


class ImageUploadReturn(TypedDict):
    src: str
    size: int
    width: int
    height: int


def upload_image_to_bucket(
    fid, image, bucket_name, width=120, height=120, output_format="WEBP", **args
) -> ImageUploadReturn:
    """
    Upload image to MinIO bucket
    :param fid: uuid
    :param image: PIL.Image
    :param bucket_name: str
    :param width: int
    :param height: int
    :param output_format: str (WEBP, JPEG, PNG)
    :param args: dict
    :return: ImageUploadReturn
    """
    # Open the image
    img = Image.open(image)

    # resize image
    img = img.resize((width, height), Image.LANCZOS)

    # Prepare the buffer for saving the image
    img_buffer = io.BytesIO()

    # Validate and set output format
    output_format = output_format.upper()
    valid_formats = ["WEBP", "JPEG", "PNG"]
    if output_format not in valid_formats:
        raise ValueError(f"Invalid format. Choose from {valid_formats}")

    # Set content type based on format
    content_type_map = {"WEBP": "image/webp", "JPEG": "image/jpeg", "PNG": "image/png"}

    # Save image with format-specific parameters
    save_params = {
        "format": output_format,
        "quality": 100 if output_format in ["JPEG", "WEBP"] else None,
    }
    img.save(img_buffer, **save_params)
    img_buffer.seek(0)

    # Save the image to the bucket
    bucket = MinioStorage()
    base_name = image.name.split(".")[-2]
    object_name = f"{fid}/{base_name}.{output_format.lower()}"

    try:
        bucket.upload(
            bucket_name=bucket_name,
            object_name=object_name,
            data=img_buffer,
            length=len(img_buffer.getvalue()),
            content_type=content_type_map[output_format],
        )
    except Exception as e:
        raise APIException(f"Error uploading image to bucket: {e}")

    payload = {
        "name": f"{base_name}.{output_format.lower()}",
        "bucket_name": bucket_name,
        "object_name": object_name,
        "width": width,
        "height": height,
    }
    return payload


def perform_create_image_file(image_file, width, height, output_format="WEBP"):
    fid = uuid.uuid4()
    uploaded_image = upload_image_to_bucket(
        fid=fid,
        image=image_file,
        width=width,
        height=height,
        output_format=output_format,
        bucket_name=settings.MINIO_PUBLIC_BUCKET,
    )
    file = File.objects.create(
        fid=fid,
        name=uploaded_image["name"],
        bucket_name=uploaded_image["bucket_name"],
        object_name=uploaded_image["object_name"],
        width=uploaded_image["width"],
        height=uploaded_image["height"],
    )
    return file
