from rest_framework import serializers
from core.models import OrderItem, Order, Offering
from api.crm.serializers.offering import CrmOfferingSerializer


class CrmOrderItemOrderSerializer(serializers.ModelSerializer):
    """Serializer for Order information in OrderItem context"""

    class Meta:
        model = Order
        fields = [
            "oid",
            "stage",
            "is_international",
            "has_full_scholarship",
        ]
        extra_kwargs = {
            "oid": {"read_only": True},
            "stage": {"read_only": True},
            "is_international": {"read_only": True},
            "has_full_scholarship": {"read_only": True},
        }


class CrmOrderItemBaseSerializer(serializers.ModelSerializer):
    """Base serializer with common fields for all OrderItem operations"""

    key = serializers.CharField(source="id", read_only=True)
    oiid = serializers.UUIDField(source="id", read_only=True)

    class Meta:
        model = OrderItem
        fields = [
            "key",
            "oiid",
            "order",
            "offering",
            "quantity",
            "custom_amount",
            "base_price",
            "foreign_base_price",
            "discount",
            "unit_price",
            "foreign_unit_price",
            "effective_unit_price",
            "total_price",
            "foreign_total_price",
            "effective_total_price",
            "has_custom_amount",
            "custom_amount_price",
            "created_at",
            "updated_at",
        ]


class CrmOrderItemListSerializer(CrmOrderItemBaseSerializer):
    """Serializer for listing order items"""

    order = CrmOrderItemOrderSerializer(read_only=True)
    offering = serializers.SerializerMethodField()

    def get_offering(self, obj):
        """Return basic offering information"""
        return {
            "ofid": obj.offering.ofid,
            "name": obj.offering.name,
            "stage": obj.offering.stage,
        }

    class Meta(CrmOrderItemBaseSerializer.Meta):
        fields = CrmOrderItemBaseSerializer.Meta.fields


class CrmOrderItemRetrieveSerializer(CrmOrderItemBaseSerializer):
    """Serializer for retrieving order item details with full nested relationships"""

    order = CrmOrderItemOrderSerializer(read_only=True)
    offering = CrmOfferingSerializer(read_only=True)

    class Meta(CrmOrderItemBaseSerializer.Meta):
        fields = CrmOrderItemBaseSerializer.Meta.fields


class CrmOrderItemCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating new order items"""

    order = serializers.UUIDField(write_only=True)
    offering = serializers.UUIDField(write_only=True)

    class Meta:
        model = OrderItem
        fields = [
            "order",
            "offering",
            "quantity",
            "custom_amount",
        ]

    def validate_order(self, value):
        """Validate that the order exists and is not deleted"""
        try:
            order = Order.objects.get(oid=value, deleted=False)
            return order
        except Order.DoesNotExist:
            raise serializers.ValidationError(f"La orden con ID {value} no existe")

    def validate_offering(self, value):
        """Validate that the offering exists and is not deleted"""
        try:
            offering = Offering.objects.get(oid=value, deleted=False)
            return offering
        except Offering.DoesNotExist:
            raise serializers.ValidationError(f"La oferta con ID {value} no existe")

    def validate_quantity(self, value):
        """Validate that quantity is positive"""
        if value <= 0:
            raise serializers.ValidationError("La cantidad debe ser mayor a cero.")
        return value

    def validate_custom_amount(self, value):
        """Validate that custom_amount is not negative"""
        if value is not None and value < 0:
            raise serializers.ValidationError(
                "El monto personalizado no puede ser negativo."
            )
        return value


class CrmOrderItemUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating existing order items"""

    class Meta:
        model = OrderItem
        fields = [
            "quantity",
            "custom_amount",
        ]

    def validate_quantity(self, value):
        """Validate that quantity is positive"""
        if value <= 0:
            raise serializers.ValidationError("La cantidad debe ser mayor a cero.")
        return value

    def validate_custom_amount(self, value):
        """Validate that custom_amount is not negative"""
        if value is not None and value < 0:
            raise serializers.ValidationError(
                "El monto personalizado no puede ser negativo."
            )
        return value
