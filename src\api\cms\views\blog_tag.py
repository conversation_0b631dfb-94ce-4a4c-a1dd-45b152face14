from rest_framework import viewsets, filters, status
from rest_framework.response import Response
from core.models import BlogTag
from api.cms.serializers.blog_tag import (
    CmsBlogTagSerializer,
    CmsCreateBlogTagSerializer,
    CmsUpdateBlogTagSerializer,
)
from api.cms.filters.blog_tag import CmsBlogTagFilter
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated, IsAdminUser, AllowAny
from api.mixins import AuditMixin, SwaggerTagMixin
from django_filters.rest_framework import DjangoFilterBackend


class CmsBlogTagViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsAdminUser]

    model_class = BlogTag
    queryset = BlogTag.objects.filter(deleted=False).order_by("name")
    serializer_class = CmsBlogTagSerializer

    filterset_class = CmsBlogTagFilter
    filter_backends = (DjangoFilterBackend, filters.OrderingFilter)
    ordering_fields = ["name", "created_at"]

    swagger_tags = ["Blog Tags"]

    def get_serializer_class(self):
        if self.action == "create":
            return CmsCreateBlogTagSerializer
        if self.action in ["update", "partial_update"]:
            return CmsUpdateBlogTagSerializer
        return super().get_serializer_class()

    def destroy(self, request, *args, **kwargs):
        """
        Override to validate if the tag is in use before deletion.
        """
        instance = self.get_object()

        # Check if the tag is being used by any non-deleted blog posts
        if instance.blog_posts.filter(deleted=False).exists():
            return Response(
                "Esta etiqueta está en uso por un blog",
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Call the parent class's destroy method to mark the tag as deleted
        return super().destroy(request, *args, **kwargs)
