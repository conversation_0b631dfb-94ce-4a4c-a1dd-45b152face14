from rest_framework import viewsets
from core.models import Instructor
from api.cms.serializers.instructor import (
    CmsInstructorSerializer,
    CmsCreateInstructorSerializer,
    CmsUpdateInstrutorSerializer,
)
from django_filters import rest_framework as filters
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from api.mixins import AuditMixin, SwaggerTagMixin
from api.cms.filters.instructor import CmsInstructorFilter


class CmsInstructorViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = Instructor
    queryset = Instructor.objects.filter(deleted=False).order_by("created_at")
    serializer_class = CmsInstructorSerializer
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsAdminUser]

    filterset_class = CmsInstructorFilter
    filter_backends = (filters.DjangoFilterBackend,)

    swagger_tags = ["Instructor"]

    def get_serializer_class(self):
        if self.action == "create":
            return CmsCreateInstructorSerializer
        if self.action in ["update", "partial_update"]:
            return CmsUpdateInstrutorSerializer
        return super().get_serializer_class()
