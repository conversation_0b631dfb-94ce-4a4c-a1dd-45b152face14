from rest_framework import viewsets
from core.models import Offering
from api.crm.serializers.offering import CrmOfferingSerializer
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from api.mixins import AuditMixin, SwaggerTagMixin


class CrmOfferingViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ReadOnlyModelViewSet,
):
    model_class = Offering
    queryset = Offering.objects.filter(deleted=False).order_by("created_at")
    serializer_class = CrmOfferingSerializer
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsAdminUser]

    swagger_tags = ["Offering"]
