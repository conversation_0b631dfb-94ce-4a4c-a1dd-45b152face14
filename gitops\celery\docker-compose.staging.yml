services:
  celery-staging:
    build:
      context: ../../
      dockerfile: gitops/celery/Dockerfile.staging
    container_name: portals-celery-staging
    command: celery -A core worker --loglevel=info
    environment:
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_PORT=${EMAIL_PORT}
      - EMAIL_USE_TLS=${EMAIL_USE_TLS}
      - EMAIL_HOST_USER=${EMAIL_HOST_USER}
      - EMAIL_HOST_PASSWORD=${EMAIL_HOST_PASSWORD}
      - DEFAULT_FROM_EMAIL=${DEFAULT_FROM_EMAIL}
    networks:
      - ceu-staging-network

  celery-beat-staging:
    build:
      context: ../../
      dockerfile: gitops/celery/Dockerfile.staging
    container_name: portals-celery-beat-staging
    command: celery -A core beat --loglevel=info
    networks:
      - ceu-staging-network

networks:
  ceu-staging-network:
    external: true

