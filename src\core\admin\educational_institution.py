from django.contrib import admin
from core.models import EducationalInstitution, Partnership


@admin.register(EducationalInstitution)
class EducationalInstitutionAdmin(admin.ModelAdmin):
    list_display = [
        "name",
        "created_at",
        "updated_at",
        "deleted",
    ]
    list_filter = ("deleted",)
    search_fields = ("name",)


@admin.register(Partnership)
class PartnershipAdmin(admin.ModelAdmin):
    list_display = [
        "name",
        "description",
        "get_institution_name",
        "get_delegate_name",
        "created_at",
        "updated_at",
        "deleted",
    ]
    list_filter = ("deleted",)
    search_fields = ("name", "description")

    def get_institution_name(self, obj):
        return obj.institution.name if obj.institution else "N/A"

    get_institution_name.short_description = "Institution Name"
    get_institution_name.admin_order_field = "institution__name"

    def get_delegate_name(self, obj):
        return obj.delegate.first_name if obj.delegate else "N/A"

    get_delegate_name.short_description = "Delegate Name"
    get_delegate_name.admin_order_field = "delegate__first_name"
