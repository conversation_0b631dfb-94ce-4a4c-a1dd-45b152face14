import re

from core.models.event_reminder import EventReminder

def build_whatsapp_payloads(template_data, contacts):
    """
    Construye el payload para enviar un mensaje de WhatsApp basado en la plantilla seleccionada.

    :param template_data: Diccionario con la información de la plantilla y variables.
        Debe contener las claves:
        - "template_name": Nombre de la plantilla.
        - "header_image_meta_url": URL de la imagen del header (opcional).
        - "variables": Diccionario con las variables a reemplazar en la plantilla.
    :param contacts: Lista de diccionarios con los datos de los contactos.
        Cada contacto debe tener las claves:
        - "whatsapp": Número de WhatsApp del contacto.
        - "name": Nombre del contacto.
    :return: Lista de payloads.
    """
    try:
        template_name = template_data.get("template_name")
        header_image_meta_url = template_data.get("header_image_meta_url")
        variables = template_data.get("variables", {})

        payloads = []
        for contact in contacts:
            payload = {
                "messaging_product": "whatsapp",
                "recipient_type": "individual",
                "to": "51987287260", # contact["WhatsApp"],  # TODO Usar el número de WhatsApp del contacto
                "type": "template",
                "template": {
                    "name": template_name.replace(" ", "_"),
                    "language": {"code": "es"},
                    "components": []
                }
            }

            # Header con imagen (si existe)
            if header_image_meta_url:
                payload["template"]["components"].append({
                    "type": "header",
                    "parameters": [
                        {
                            "type": "image",
                            "image": {
                                "link": header_image_meta_url
                            }
                        }
                    ]
                })

            # Variables del body
            body_parameters = []
            if "body" in variables:
                for value in variables["body"]:
                    if value == '[[name]]':
                        value = contact["name"]  # Reemplaza con el nombre del contacto
                    body_parameters.append({"type": "text", "text": value})

            if body_parameters:
                payload["template"]["components"].append({
                    "type": "body",
                    "parameters": body_parameters
                })

            # Variables en los botones (si existen)
            if "buttons" in variables:
                buttons = variables["buttons"]
                for button in buttons:
                    button_component = {
                        "type": "button",
                        "sub_type": "url",  # Cambiado a "url" para botones de tipo URL
                        "index": button["index"],  # Índice dinámico basado en la iteración
                        "parameters": [
                            {
                                "type": "text",
                                "text": button["variable"]  # El texto del botón
                            }
                        ]
                    }
                    payload["template"]["components"].append(button_component)

            payloads.append(payload)

        return payloads

    except Exception as e:
        print("🔴 ERROR: ", e)
        return {}
def clean_whatsapp_numbers(data):
    """Limpia los números de WhatsApp y elimina duplicados."""
    seen_numbers = set()
    cleaned_data = []

    for item in data:
        phone_number = item.get("WhatsApp", "")
        phone_number = re.sub(r"\D", "", phone_number)  # Elimina caracteres no numéricos

        if len(phone_number) >= 9 and phone_number not in seen_numbers:
            seen_numbers.add(phone_number)
            names = item.get("Nombres", "").strip() or item.get("Nombre completo", "").strip()
            cleaned_data.append({"Names": names, "WhatsApp": phone_number})

    return cleaned_data


def prepare_whatsapp_numbers(data):
    """Transforma los datos cambiando el nombre de la columna y extrayendo la primera palabra."""
    transformed_data = []

    for item in data:
        names = item.get("Names", "").strip()
        first_name = names.split()[0] if names else "Estimado"

        transformed_data.append({"name": first_name, "WhatsApp": item["WhatsApp"]})

    return transformed_data