from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from api.mixins import AuditMixin, SwaggerTagMixin
from core.models import EventSchedule
from api.crm.serializers.event_schedule import (
    CrmEventScheduleListItemSerializer,
    CrmEventScheduleRetrieveSerializer,
    CrmEventScheduleCreateSerializer,
    CrmEventScheduleUpdateSerializer,
    CrmEventScheduleFileSerializer,
)
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from services.google.event import GoogleEventsManager
from django.conf import settings
from django.utils import timezone

class CrmEventScheduleViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = EventSchedule
    queryset = EventSchedule.objects.filter(deleted=False).order_by("-created_at")
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsAdminUser]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = [
        "stage",
        "modality",
        "event",
        "instructor",
        "start_date",
        "end_date",
    ]
    search_fields = ["name", "description", "location"]
    ordering_fields = [
        "name",
        "created_at",
        "updated_at",
        "start_date",
        "end_date",
        "price",
    ]
    ordering = ["-created_at"]

    swagger_tags = ["Event Schedules"]

    def get_serializer_class(self):
        if self.action == "list":
            return CrmEventScheduleListItemSerializer
        elif self.action == "retrieve":
            return CrmEventScheduleRetrieveSerializer
        elif self.action == "create":
            return CrmEventScheduleCreateSerializer
        elif self.action in ["update", "partial_update"]:
            return CrmEventScheduleUpdateSerializer
        return CrmEventScheduleListItemSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)

        # get event instance
        event_schedule = serializer.instance

        try:
            # Create Google Calendar event
            google_events_manager = GoogleEventsManager()
            start_datetime = event_schedule.start_date.strftime("%Y-%m-%dT%H:%M:%S")
            end_datetime = event_schedule.end_date.strftime("%Y-%m-%dT%H:%M:%S")

            event = google_events_manager.create_event(
                calendar_id=settings.GOOGLE_CALENDAR_ID,
                summary=event_schedule.name,
                description=event_schedule.description,
                start_datetime=start_datetime,
                end_datetime=end_datetime,
                timezone="America/Lima",
                location=event_schedule.location,
                create_meet=True,
            )

            if event:
                # Save the Google Calendar event ID to the EventSchedule instance
                # event_schedule.google_event_id = event.get("id")
                # TODO: Save the event ID to the model if needed
                event_schedule.save()
        except Exception as e:
            print(f"Error creating event in Google Calendar: {e}")

        retrieve_serializer = CrmEventScheduleRetrieveSerializer(serializer.instance)
        return Response(retrieve_serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=["post"])
    def enroll(self, request, pk=None):
        """Enroll a user in an event schedule"""
        event_schedule = self.get_object()
        user = request.user

        # Check if the user is already enrolled
        if event_schedule.enrolled_users.filter(id=user.id).exists():
            return Response(
                {"detail": "User is already enrolled in this event schedule."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Enroll the user
        event_schedule.enrolled_users.add(user)
        return Response(
            {"detail": "User enrolled successfully."}, status=status.HTTP_200_OK
        )

    @action(
        detail=True,
        methods=["POST"],
        url_path="upload-thumbnail",
    )
    def upload_thumbnail(self, request, pk=None):
        event = self.get_object()
        serializer = CrmEventScheduleFileSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        file = serializer.save()

        if event.thumbnail:
            event.thumbnail.delete()

        event.thumbnail = file
        event.save()
        return Response(
            status=status.HTTP_200_OK,
            data=FileSerializer(file).data,
        )

    @action(
        detail=True,
        methods=["DELETE"],
        url_path="remove-thumbnail/(?P<fid>[^/.]+)",
    )
    def remove_thumbnail(self, request, pk=None, fid=None):
        event = self.get_object()
        if event.thumbnail and event.thumbnail.fid == fid:
            event.thumbnail.delete()
            event.thumbnail = None
            event.save()
            return Response(status=status.HTTP_204_NO_CONTENT)
        return Response(status=status.HTTP_404_NOT_FOUND)

    @action(
        detail=True,
        methods=["POST"],
        url_path="upload-cover-image",
    )
    def upload_cover_image(self, request, pk=None):
        event = self.get_object()
        serializer = CrmEventScheduleFileSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        file = serializer.save()

        if event.cover_image:
            event.cover_image.delete()

        event.cover_image = file
        event.save()

        file.is_used = True
        file.save()
        return Response(
            status=status.HTTP_200_OK,
            data=FileSerializer(file).data,
        )

    @action(
        detail=True,
        methods=["DELETE"],
        url_path="remove-cover-image/(?P<fid>[^/.]+)",
    )
    def remove_cover_image(self, request, pk=None, fid=None):
        event = self.get_object()
        if event.cover_image and str(event.cover_image.fid) == fid:
            event.cover_image.delete()
            event.cover_image = None
            event.save()
            return Response(
                status=status.HTTP_204_NO_CONTENT,
            )
        return Response(status=status.HTTP_404_NOT_FOUND)

    @action(
        detail=False,
        methods=["GET"],
        url_path="list-google-events",
    )
    def list_event_schedules(self, request, *args, **kwargs):
        google_events_manager = GoogleEventsManager()

        # timezone lima peru
        tz = "America/Lima"

        time_now = timezone.now()
        time_end = time_now + timezone.timedelta(hours=1)

        # Convert to string format
        time_now = time_now.strftime("%Y-%m-%dT%H:%M:%S")
        time_end = time_end.strftime("%Y-%m-%dT%H:%M:%S")

        event = google_events_manager.create_event(
            calendar_id=settings.GOOGLE_CALENDAR_ID,
            summary="Test Event",
            description="This is a test event.",
            start_datetime=time_now,
            end_datetime=time_end,
            timezone="Etc/UTC",
            location="Test Location",
            create_meet=True,
        )
        if event is None:
            return Response(
                {"detail": "Error creating event in Google Calendar."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        return Response(event, status=status.HTTP_200_OK)
