services:
  redis:
    image: redis:7-alpine
    container_name: staging-portals-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - ceu-staging-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  redis_data:

networks:
  ceu-staging-network:
    external: true