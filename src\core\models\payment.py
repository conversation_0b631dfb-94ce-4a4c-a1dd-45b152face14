import uuid
from django.db import models
from .base import AuditBaseModel


class Payment(AuditBaseModel):
    USD_CURRENCY = "usd"
    PEN_CURRENCY = "pen"
    CURRENCY_CHOICES = [
        (USD_CURRENCY, "USD"),
        (PEN_CURRENCY, "PEN"),
    ]
    pid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    order = models.ForeignKey(
        "Order",
        on_delete=models.CASCADE,
        related_name="payments",
        null=True,
        blank=True,
        verbose_name="Order",
    )
    is_paid = models.BooleanField(
        default=False,
        null=False,
        blank=False,
        verbose_name="Is Paid",
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        null=False,
        blank=False,
        verbose_name="Amount",
    )
    payment_date = models.DateTimeField(
        auto_now_add=True,
        null=True,
        blank=True,
        verbose_name="Payment Date",
    )
    scheduled_payment_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="Scheduled Payment Date",
    )
    ext_payment_id = models.CharField(
        max_length=64,
        null=True,
        blank=True,
        verbose_name="External Payment ID",
    )
    currency = models.CharField(
        max_length=3,
        choices=CURRENCY_CHOICES,
        default=PEN_CURRENCY,
        null=False,
        blank=False,
        verbose_name="Currency",
    )
    voucher = models.ForeignKey(
        "File",
        on_delete=models.SET_NULL,
        related_name="payments",
        null=True,
        blank=True,
        verbose_name="Voucher",
    )
    payment_method = models.ForeignKey(
        "PaymentMethod",
        on_delete=models.SET_NULL,
        related_name="payments",
        null=True,
        blank=True,
    )
    is_first_payment = models.BooleanField(
        default=False,
        null=False,
        blank=False,
        verbose_name="Is First Payment",
    )
    is_refund = models.BooleanField(
        default=False,
        null=False,
        blank=False,
        verbose_name="Is Refund",
    )
    observations = models.TextField(
        null=True,
        blank=True,
        verbose_name="Observations",
    )


class PaymentMethod(AuditBaseModel):
    pmid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        verbose_name="Payment Method ID",
    )
    name = models.CharField(
        max_length=255,
        null=False,
        blank=False,
        verbose_name="Name",
    )
    description = models.TextField(
        null=True,
        blank=True,
        verbose_name="Description",
    )
