from rest_framework import viewsets, filters
from core.models import BlogPost
from api.cms.serializers.blog import (
    CmsBlogPostListSerializer,
    CmsBlogPostDetailSerializer,
    CmsCreateBlogPostSerializer,
    CmsUpdateBlogPostSerializer,
    CmsDeleteImageSerializer,
)
from api.cms.filters.blog import CmsB<PERSON><PERSON><PERSON>Filter
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.response import Response
from rest_framework import status
from api.mixins import AuditMixin, SwaggerTagMixin
from django_filters.rest_framework import DjangoFilterBackend
from api.paginations import StandardResultsPagination
from rest_framework.decorators import action


class CmsBlogPostViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsAdminUser]

    model_class = BlogPost
    queryset = BlogPost.objects.filter(deleted=False).order_by("-created_at")
    pagination_class = StandardResultsPagination

    filterset_class = CmsBlogPostFilter
    filter_backends = (DjangoFilterBackend, filters.OrderingFilter)
    ordering_fields = [
        "title",
        "created_at",
        "published_at",
        "view_count",
        "featured_order",
    ]

    swagger_tags = ["Blog Posts"]

    def get_serializer_class(self):
        if self.action == "list":
            return CmsBlogPostListSerializer
        if self.action == "retrieve":
            return CmsBlogPostDetailSerializer
        if self.action == "create":
            return CmsCreateBlogPostSerializer
        if self.action in ["update", "partial_update"]:
            return CmsUpdateBlogPostSerializer
        if self.action == "delete_image":
            return CmsDeleteImageSerializer
        # Default to detail serializer for other actions
        return CmsBlogPostDetailSerializer

    def perform_destroy(self, instance):
        """
        Override to delete associated images when a blog post is deleted.
        """
        # Delete cover image if it exists
        if instance.cover_image:
            cover_image = instance.cover_image
            instance.cover_image = None
            cover_image.delete()

        # Delete thumbnail if it exists
        if instance.thumbnail:
            thumbnail = instance.thumbnail
            instance.thumbnail = None
            thumbnail.delete()

        # Call the parent class's perform_destroy to mark the blog post as deleted
        super().perform_destroy(instance)

    @action(detail=True, methods=["patch"], url_path="delete-image")
    def delete_image(self, request, pk=None):
        type = request.data.get("type")
        blog_post = self.get_object()

        if type == "cover" and blog_post.cover_image:
            blog_post.cover_image.delete()
            blog_post.cover_image = None
            blog_post.save()
            return Response(status=status.HTTP_200_OK)

        if type == "thumbnail" and blog_post.thumbnail:
            blog_post.thumbnail.delete()
            blog_post.thumbnail = None
            blog_post.save()
            return Response(status=status.HTTP_200_OK)

        return Response(
            {"detail": "No cover image to remove."}, status=status.HTTP_400_BAD_REQUEST
        )
