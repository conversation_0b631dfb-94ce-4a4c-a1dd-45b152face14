import json
from rest_framework import serializers
from core.models import (
    Offering,
    User,
    Topic,
    ModuleCourse,
    OfferingModule,
    Instructor,
)
from api.shared.serializers.file import FileSerializer
from core.constants import (
    OFFERING_THUMBNAIL_WIDTH,
    OFFERING_THUMBNAIL_HEIGHT,
)
from django.utils import timezone
from api.utils import (
    perform_create_image_file,
)
from django.core.exceptions import ValidationError


class TopicSerializer(serializers.ModelSerializer):
    key = serializers.UUIDField(source="tid", read_only=True)

    class Meta:
        model = Topic
        fields = [
            "key",
            "tid",
            "title",
        ]


class ModuleCourseSerializer(serializers.ModelSerializer):
    topics = TopicSerializer(many=True, read_only=True)
    key = serializers.UUIDField(source="mcid", read_only=True)

    class Meta:
        model = ModuleCourse
        fields = [
            "key",
            "mcid",
            "title",
            "topics",
        ]


class OfferingModuleSerializer(serializers.ModelSerializer):
    courses = ModuleCourseSerializer(many=True, read_only=True)
    key = serializers.UUIDField(source="omid", read_only=True)

    class Meta:
        model = OfferingModule
        fields = [
            "key",
            "omid",
            "title",
            "courses",
        ]


class CmsOfferingSerializer(serializers.ModelSerializer):
    key = serializers.UUIDField(source="oid", read_only=True)
    thumbnail = FileSerializer(read_only=True, allow_null=True)
    final_price = serializers.SerializerMethodField()
    foreign_final_price = serializers.SerializerMethodField()
    base_price = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        coerce_to_string=False,
    )
    foreign_base_price = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        coerce_to_string=False,
    )
    discount = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        coerce_to_string=False,
    )
    modules = OfferingModuleSerializer(many=True, read_only=True)

    class Meta:
        model = Offering
        exclude = [
            "deleted",
            "deleted_at",
            "deleted_by",
            "enrollments",
        ]

    def get_final_price(self, obj) -> float:
        return obj.base_price - obj.discount * obj.base_price / 100

    def get_foreign_final_price(self, obj) -> float:
        return obj.foreign_base_price - obj.discount * obj.foreign_base_price / 100


class CmsCreateOfferingSerializer(CmsOfferingSerializer):
    thumbnail_file = serializers.FileField(write_only=True, required=False)

    def create(self, validated_data):
        thumbnail_file = validated_data.pop("thumbnail_file", None)
        offering: Offering = Offering.objects.create(**validated_data)

        if thumbnail_file:
            thumbnail = perform_create_image_file(
                thumbnail_file,
                OFFERING_THUMBNAIL_WIDTH,
                OFFERING_THUMBNAIL_HEIGHT,
            )
            offering.thumbnail = thumbnail

        offering.save()
        return offering


class CmsUpdateOfferingSerializer(CmsOfferingSerializer):
    thumbnail_file = serializers.FileField(write_only=True, required=False)
    delete_thumbnail = serializers.BooleanField(write_only=True, required=False)
    modules = serializers.CharField(write_only=True, required=False)
    instructors = serializers.CharField(write_only=True, required=False)

    def validate_modules(self, value):
        """
        Validate and parse the modules JSON string
        """
        try:
            return json.loads(value)
        except json.JSONDecodeError:
            raise ValidationError("Invalid JSON format for modules")
        except TypeError:
            return None

    def update(self, instance, validated_data):
        instructors_data = validated_data.pop("instructors", None)
        modules_data = validated_data.pop("modules", None)
        thumbnail_file = validated_data.pop("thumbnail_file", None)
        delete_thumbnail = validated_data.pop("delete_thumbnail", False)

        offering: Offering = super().update(instance, validated_data)

        if modules_data is not None:
            self._handle_modules_update(offering, modules_data)

        if instructors_data is not None:
            self._handle_instructors_update(offering, instructors_data)

        if delete_thumbnail:
            self.__perform_delete_thumbnail(offering, self.context["request"].user)

        if thumbnail_file:
            if offering.thumbnail:
                self.__perform_delete_thumbnail(offering, self.context["request"].user)

            thumbnail = perform_create_image_file(
                thumbnail_file,
                OFFERING_THUMBNAIL_WIDTH,
                OFFERING_THUMBNAIL_HEIGHT,
            )
            offering.thumbnail = thumbnail
        offering.save()
        return offering

    def _handle_instructors_update(self, offering, instructors_data):
        if isinstance(instructors_data, str):
            instructors_data = json.loads(instructors_data)
        new_instructor_ids = set(instructor["iid"] for instructor in instructors_data)

        new_instructor_ids = [instructor["iid"] for instructor in instructors_data]
        offering.instructors.set(Instructor.objects.filter(iid__in=new_instructor_ids))

    def __perform_delete_thumbnail(
        self,
        offering: Offering,
        user: User,
    ):
        offering.thumbnail.deleted = True
        offering.thumbnail.deleted_at = timezone.now()
        offering.thumbnail.deleted_by = user
        offering.thumbnail.save()
        offering.thumbnail = None

    def _handle_modules_update(self, offering, modules_data):
        # Get existing modules
        existing_modules = {
            str(module.omid): module for module in offering.modules.all()
        }
        processed_modules = set()

        for module_data in modules_data:
            module_id = module_data.get("omid")

            if module_id and module_id in existing_modules:
                # Update existing module
                module = existing_modules[module_id]
                module.title = module_data.get("title", module.title)
                module.save()
                processed_modules.add(module_id)

                # Handle courses for this module
                self._handle_courses_update(module, module_data.get("courses", []))
            else:
                # Create new module
                new_module = OfferingModule.objects.create(
                    offering=offering, title=module_data.get("title")
                )
                self._handle_courses_update(new_module, module_data.get("courses", []))

        # Delete modules that weren't in the update data
        modules_to_delete = set(existing_modules.keys()) - processed_modules
        OfferingModule.objects.filter(omid__in=modules_to_delete).delete()

    def _handle_courses_update(self, module, courses_data):
        existing_courses = {str(course.mcid): course for course in module.courses.all()}
        processed_courses = set()

        for course_data in courses_data:
            course_id = course_data.get("mcid")

            if course_id and course_id in existing_courses:
                # Update existing course
                course = existing_courses[course_id]
                course.title = course_data.get("title", course.title)
                course.save()
                processed_courses.add(course_id)

                # Handle topics for this course
                self._handle_topics_update(course, course_data.get("topics", []))
            else:
                # Create new course
                new_course = ModuleCourse.objects.create(
                    module=module, title=course_data.get("title")
                )
                self._handle_topics_update(new_course, course_data.get("topics", []))

        # Delete courses that weren't in the update data
        courses_to_delete = set(existing_courses.keys()) - processed_courses
        ModuleCourse.objects.filter(mcid__in=courses_to_delete).delete()

    def _handle_topics_update(self, course, topics_data):
        existing_topics = {str(topic.tid): topic for topic in course.topics.all()}
        processed_topics = set()

        for topic_data in topics_data:
            topic_id = topic_data.get("tid")

            if topic_id and topic_id in existing_topics:
                # Update existing topic
                topic = existing_topics[topic_id]
                topic.title = topic_data.get("title", topic.title)
                topic.save()
                processed_topics.add(topic_id)
            else:
                # Create new topic
                Topic.objects.create(course=course, title=topic_data.get("title"))

        # Delete topics that weren't in the update data
        topics_to_delete = set(existing_topics.keys()) - processed_topics
        Topic.objects.filter(tid__in=topics_to_delete).delete()
