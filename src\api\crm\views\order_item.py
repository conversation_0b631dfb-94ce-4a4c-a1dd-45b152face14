from core.models import OrderItem
from rest_framework import viewsets, status, filters
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.crm.serializers.order_item import (
    CrmOrderItemBaseSerializer,
    CrmOrderItemListSerializer,
    CrmOrderItemRetrieveSerializer,
    CrmOrderItemCreateSerializer,
    CrmOrderItemUpdateSerializer,
)
from api.mixins import AuditMixin, SwaggerTagMixin
from django_filters.rest_framework import DjangoFilterBackend
from api.paginations import StandardResultsPagination
from api.permissions import IsStaffUser


class CrmOrderItemViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = OrderItem
    queryset = OrderItem.objects.filter(deleted=False).order_by("-created_at")
    serializer_class = CrmOrderItemBaseSerializer

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]
    pagination_class = StandardResultsPagination

    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    )
    ordering_fields = ["created_at", "quantity", "effective_total_price"]
    filterset_fields = [
        "order",
        "offering",
        "order__stage",
        "order__is_international",
    ]
    search_fields = [
        "order__owner__first_name",
        "order__owner__last_name",
        "order__owner__email",
        "order__owner__phone_number",
        "offering__name",
    ]

    swagger_tags = ["Order Items"]

    def get_serializer(self, *args, **kwargs):
        """Return appropriate serializer based on action"""
        if self.action == "create":
            return CrmOrderItemCreateSerializer(*args, **kwargs)
        elif self.action in ["update", "partial_update"]:
            return CrmOrderItemUpdateSerializer(*args, **kwargs)
        elif self.action == "retrieve":
            return CrmOrderItemRetrieveSerializer(*args, **kwargs)
        elif self.action == "list":
            return CrmOrderItemListSerializer(*args, **kwargs)
        return CrmOrderItemBaseSerializer(*args, **kwargs)

    def create(self, request, *args, **kwargs):
        """Create a new order item"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)

        # Return detailed view of created item
        retrieve_serializer = CrmOrderItemRetrieveSerializer(serializer.instance)
        return Response(retrieve_serializer.data, status=status.HTTP_201_CREATED)

    def update(self, request, *args, **kwargs):
        """Update an existing order item"""
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        # Return detailed view of updated item
        retrieve_serializer = CrmOrderItemRetrieveSerializer(serializer.instance)
        return Response(retrieve_serializer.data)

    def destroy(self, request, *args, **kwargs):
        """Soft delete an order item"""
        instance = self.get_object()

        # Check if order is in a state that allows item deletion
        if instance.order.stage == instance.order.SOLD_STAGE:
            return Response(
                {"error": "Cannot delete items from sold orders"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Perform soft delete
        self.perform_destroy(instance)
        return Response(status=status.HTTP_204_NO_CONTENT)
