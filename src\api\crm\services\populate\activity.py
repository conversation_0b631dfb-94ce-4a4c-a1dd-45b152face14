from core.models import (
    Activity,
    Order,
    User,
)
import json
from datetime import datetime

POPULATE_CRM_ASSETS_DIR = "api/crm/services/populate/assets"
activities_json_file_path = f"{POPULATE_CRM_ASSETS_DIR}/13_activities.json"


def populate_activity_data():
    with open(activities_json_file_path) as f:
        data = json.load(f)

        activities = [
            Activity(
                aid=item["aid"],
                title=item["title"],
                description=item["description"],
                deadline=(
                    datetime.strptime(item["deadline"], "%Y-%m-%d %H:%M:%S")
                    if item.get("deadline")
                    else None
                ),
                responsible=(
                    User.objects.get(uid=item["assigned_to"])
                    if item.get("assigned_to")
                    else None
                ),
                status=item["status"],
                order=(
                    Order.objects.get(oid=item["order"]) if item.get("order") else None
                ),
            )
            for item in data
        ]

        Activity.objects.bulk_create(activities)
