from rest_framework import serializers
from core.models import Payment, Order, File, PaymentMethod, User
from api.shared.serializers.file import FileSerializer


class CrmPaymentOwnerSerializer(serializers.ModelSerializer):
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "uid",
            "full_name",
            "email",
            "phone_number",
        ]

    def get_full_name(self, obj):
        return obj.get_full_name() if obj.get_full_name() != "" else "Sin nombre"


class CrmPaymentOrderSerializer(serializers.ModelSerializer):
    owner = CrmPaymentOwnerSerializer(read_only=True)

    class Meta:
        model = Order
        fields = [
            "oid",
            "stage",
            "owner",
        ]

        extra_kwargs = {
            "oid": {"read_only": True},
            "stage": {"read_only": True},
        }


class CrmPaymentPaymentMethodSerializer(serializers.ModelSerializer):
    class Meta:
        model = PaymentMethod
        fields = [
            "pmid",
            "name",
        ]

        extra_kwargs = {
            "pmid": {"read_only": True},
            "name": {"read_only": True},
        }


class CrmPaymentBaseSerializer(serializers.ModelSerializer):
    """Base serializer with common fields for all operations"""

    key = serializers.CharField(source="pid", read_only=True)

    class Meta:
        model = Payment
        fields = [
            "key",
            "pid",
            "order",
            "payment_method",
            "is_paid",
            "amount",
            "currency",
            "payment_date",
            "created_at",
            "updated_at",
        ]

        extra_kwargs = {
            "pid": {"read_only": True},
            "created_at": {"read_only": True},
            "updated_at": {"read_only": True},
        }


class CrmPaymentListItemSerializer(CrmPaymentBaseSerializer):
    """Serializer for listing payments"""

    order = CrmPaymentOrderSerializer(read_only=True)
    payment_method = CrmPaymentPaymentMethodSerializer(read_only=True)

    class Meta(CrmPaymentBaseSerializer.Meta):
        fields = CrmPaymentBaseSerializer.Meta.fields


class CrmPaymentRetrieveSerializer(CrmPaymentBaseSerializer):
    """Serializer for retrieving payment details with nested relationships"""

    order = CrmPaymentOrderSerializer(read_only=True)
    payment_method = CrmPaymentPaymentMethodSerializer(read_only=True)
    voucher = FileSerializer(read_only=True)

    class Meta(CrmPaymentBaseSerializer.Meta):
        fields = CrmPaymentBaseSerializer.Meta.fields + [
            "voucher",
        ]


class CrmPaymentCreateSerializer(CrmPaymentBaseSerializer):
    """Serializer for creating new payments"""

    order = serializers.UUIDField(required=True)
    is_paid = serializers.BooleanField(required=True)
    currency = serializers.CharField(max_length=3, required=True)
    amount = serializers.DecimalField(max_digits=10, decimal_places=2, required=True)
    payment_method = serializers.UUIDField(required=False, allow_null=True)
    payment_date = serializers.DateTimeField(required=True)
    voucher = serializers.UUIDField(required=False, allow_null=True)

    class Meta(CrmPaymentBaseSerializer.Meta):
        fields = [
            "order",
            "is_paid",
            "currency",
            "amount",
            "payment_method",
            "payment_date",
            "voucher",
        ]

    def validate_order(self, value):
        try:
            order = Order.objects.get(oid=value, deleted=False)
            return order
        except Order.DoesNotExist:
            raise serializers.ValidationError(f"Order with ID {value} does not exist")

    def validate_payment_method(self, value):
        if value:
            try:
                payment_method = PaymentMethod.objects.get(pmid=value, deleted=False)
                return payment_method
            except PaymentMethod.DoesNotExist:
                raise serializers.ValidationError(
                    f"Payment method with ID {value} does not exist"
                )
        return None

    def validate_currency(self, value):
        valid_currencies = ["pen", "usd"]
        if value.lower() not in valid_currencies:
            raise serializers.ValidationError(
                f"Invalid currency. Must be one of: {', '.join(valid_currencies)}"
            )
        return value.lower()

    def validate_amount(self, value):
        if value < 0:
            raise serializers.ValidationError("Amount cannot be negative")
        return value

    def validate_voucher(self, value):
        if value:
            try:
                voucher = File.objects.get(fid=value, deleted=False)
                if voucher:
                    voucher.is_used = True
                    voucher.save()
                return voucher
            except File.DoesNotExist:
                raise serializers.ValidationError(
                    f"Voucher with ID {value} does not exist"
                )
        return None

    def create(self, validated_data):
        return super().create(validated_data)


class CrmPaymentUpdateSerializer(CrmPaymentBaseSerializer):
    """Serializer for updating existing payments"""

    is_paid = serializers.BooleanField(required=False)
    amount = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    ext_payment_id = serializers.CharField(
        max_length=64, required=False, allow_null=True
    )
    voucher = serializers.PrimaryKeyRelatedField(
        queryset=File.objects.all(), required=False, allow_null=True
    )
    payment_method = serializers.PrimaryKeyRelatedField(
        queryset=PaymentMethod.objects.all(), required=False, allow_null=True
    )

    class Meta(CrmPaymentBaseSerializer.Meta):
        fields = CrmPaymentBaseSerializer.Meta.fields + [
            "voucher",
            "payment_method",
            "ext_payment_id",
        ]
