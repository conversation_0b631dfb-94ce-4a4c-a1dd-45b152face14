import requests
from rest_framework import serializers
from core.models import EventReminder
from django.conf import settings
from api.crm.utils import build_whatsapp_payloads, clean_whatsapp_numbers, prepare_whatsapp_numbers
from api.crm.serializers.template import CrmTemplateSerializer
from api.crm.services.events_alliances import get_event_registrations_alliances


class CrmEventReminderSerializer(serializers.ModelSerializer):
    template = serializers.SerializerMethodField()

    class Meta:
        model = EventReminder
        fields = ["event_alliance_id", "reminder_type", "event_name", "template_id", "template", "variables", "send_at", "status", "rid", "created_at", "updated_at"]

    def get_template(self, obj):
        return CrmTemplateSerializer(obj.template_id).data if obj.template_id else None

class CrmSendEventReminderSerializer(CrmEventReminderSerializer):
    def send_event_reminder(self, event_reminder: EventReminder):
        """Envía un recordatorio de evento por WhatsApp."""
        reminders_sent = 0
        try:
            if event_reminder.status == EventReminder.SENT:
                raise Exception("Reminder already sent")

            # 1. Obtener contactos inscritos en el evento desde API externa
            event_registrations = get_event_registrations_alliances(event_reminder.event_alliance_id)
            # limpiar data
            event_registrations = clean_whatsapp_numbers(event_registrations)
            # preparar data
            event_registrations = prepare_whatsapp_numbers(event_registrations)
            if not event_registrations:
                event_reminder.status = EventReminder.FAILED
                event_reminder.save()
                raise Exception("No registrations found")

            event_reminder = EventReminder.objects.select_related("template_id").get(rid=event_reminder.rid)
            template_data = {
                "template_name": event_reminder.template_id.name,
                "header_image_meta_url": event_reminder.template_id.header_image_meta_url,
                "variables": event_reminder.variables or {}
            }

            # 2. Construir el mensaje de WhatsApp basado en la plantilla
            payloads = build_whatsapp_payloads(template_data, event_registrations)
            event_reminder.save()
            # 3. Enviar mensajes a través de la API de WhatsApp
            errors = []
            for payload in payloads:
                response = requests.post(
                    f"https://graph.facebook.com/v21.0/{settings.WHATSAPP_BUSINESS_PHONE_ID}/messages",
                    headers={
                        "Authorization": f"Bearer {settings.META_ACCESS_TOKEN}",
                        "Content-Type": "application/json",
                    },
                    json=payload,
                )

                response_json = response.json()
                if response.status_code != 200:
                    # event_reminder.status = EventReminder.FAILED
                    event_reminder.status = EventReminder.DRAFT
                    event_reminder.save()
                    base_error = f"Error al enviar recordatorio al número {payload['to']}:"
                    if 'error' in response_json and 'message' in response_json['error']:
                        errors.append(f"{base_error} {response_json['error']['message']}") 
                    else:
                        errors.append(f"{base_error} {str(response_json)}") 

                # interrumpir luego de solo una iteración
                reminders_sent += 1
                break # TODO: BORRAR ESTA LINEA en produccion

            if errors and reminders_sent == 0:
                raise Exception(errors)
            # 4. Actualizar estado a 'SENT'
            event_reminder.status = EventReminder.SENT
            event_reminder.save()
            if errors and reminders_sent > 0:
                return {
                    "detail": f"Reminder {event_reminder.rid} sent successfully!",
                    "count": reminders_sent,
                    "errors": errors
                }

            return {
                "detail": f"Reminder {event_reminder.rid} sent successfully!",
                "count": reminders_sent
            }

        except Exception as e:
            event_reminder.status = EventReminder.DRAFT
            event_reminder.save()
            raise e
class CrmCreateEventReminderSerializer(CrmEventReminderSerializer):
    class Meta:
        model = EventReminder
        fields = ["event_alliance_id", "template_id", "send_at", "event_name", "reminder_type"]
class CrmUpdateEventReminderSerializer(CrmEventReminderSerializer):
    class Meta:
        model = EventReminder
        fields = ["event_alliance_id", "template_id", "send_at", "variables", "event_name", "reminder_type"]