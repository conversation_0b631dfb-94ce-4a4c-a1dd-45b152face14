from rest_framework import serializers
from core.models import User as Contact, EducationalInstitution, Major, Term
from api.shared.serializers.file import FileSerializer


class CrmContactEducationalInstitutionSerializer(serializers.ModelSerializer):
    class Meta:
        model = EducationalInstitution
        fields = [
            "eiid",
            "name",
            "acronym",
        ]


class CrmContactMajorSerializer(serializers.ModelSerializer):
    class Meta:
        model = Major
        fields = [
            "mid",
            "name",
        ]


class CrmContactTermSerializer(serializers.ModelSerializer):
    class Meta:
        model = Term
        fields = [
            "tid",
            "name",
        ]


class CrmBaseContactSerializer(serializers.ModelSerializer):
    key = serializers.CharField(source="uid", read_only=True)
    full_name = serializers.SerializerMethodField(
        source="get_full_name",
        read_only=True,
    )

    def get_full_name(self, obj):
        return obj.get_full_name() if obj.get_full_name() != "" else "Sin nombre"

    class Meta:
        model = Contact
        fields = [
            "uid",
            "key",
            "username",
            "full_name",
            "is_active",
            "created_at",
            "updated_at",
        ]

        extra_kwargs = {
            "uid": {"read_only": True},
            "username": {"read_only": True},
            "is_active": {"read_only": True},
            "created_at": {"read_only": True},
            "updated_at": {"read_only": True},
        }


class CrmContactListItemSerializer(CrmBaseContactSerializer):
    profile_photo = FileSerializer(read_only=True)
    educational_institution = CrmContactEducationalInstitutionSerializer(
        read_only=True,
    )

    major = serializers.CharField(
        source="major.name",
        read_only=True,
    )

    term = serializers.CharField(
        source="term.name",
        read_only=True,
    )

    class Meta(CrmBaseContactSerializer.Meta):
        fields = CrmBaseContactSerializer.Meta.fields + [
            "first_name",
            "last_name",
            "email",
            "phone_number",
            "ocupation",
            "company",
            "role",
            "profile_photo",
            "educational_institution",
            "major",
            "term",
            "is_staff",
            "google_contact_id",
            "last_google_sync",
        ]
        extra_kwargs = {
            "google_contact_id": {"read_only": True},
            "last_google_sync": {"read_only": True},
        }


class CrmRetrieveContactSerializer(CrmBaseContactSerializer):
    profile_photo = FileSerializer(read_only=True)

    educational_institution = CrmContactEducationalInstitutionSerializer()
    major = CrmContactMajorSerializer()
    term = CrmContactTermSerializer()

    class Meta(CrmBaseContactSerializer.Meta):
        fields = CrmBaseContactSerializer.Meta.fields + [
            "first_name",
            "last_name",
            "email",
            "phone_number",
            "ocupation",
            "company",
            "role",
            "profile_photo",
            "educational_institution",
            "major",
            "term",
            "is_staff",
            "google_contact_id",
            "last_google_sync",
        ]


class CrmCreateContactSerializer(serializers.ModelSerializer):
    first_name = serializers.CharField(required=False)
    last_name = serializers.CharField(required=False)
    phone_number = serializers.CharField(required=True)
    email = serializers.EmailField(required=False)
    educational_institution = serializers.CharField(required=False)

    def validate_phone_number(self, value):
        if not value:
            return value

        # if the phone number exists but is deleted raise an error with a custom message
        if Contact.objects.filter(phone_number=value, deleted=True).exists():
            raise serializers.ValidationError(
                "El número de teléfono ya existe pero está eliminado. "
                "Por favor, contacta al administrador para restaurarlo.",
            )
        # if the phone number exists and is not deleted raise an error with a custom message
        if Contact.objects.filter(phone_number=value, deleted=False).exists():
            raise serializers.ValidationError(
                "El número de teléfono ya existe. "
                "Por favor, utiliza otro número de teléfono.",
            )
        return value

    def validate_email(self, value):
        if not value:
            return value

        # if the email exists but is deleted raise an error with a custom message
        if Contact.objects.filter(email=value, deleted=True).exists():
            raise serializers.ValidationError(
                "El correo electrónico ya existe pero está eliminado. "
                "Por favor, contacta al administrador para restaurarlo.",
            )
        # if the email exists and is not deleted raise an error with a custom message
        if Contact.objects.filter(email=value, deleted=False).exists():
            raise serializers.ValidationError(
                "El correo electrónico ya existe. "
                "Por favor, utiliza otro correo electrónico.",
            )
        return value

    def validate_educational_institution(self, value):
        if value:
            try:
                educational_institution = EducationalInstitution.objects.get(eiid=value)
                return educational_institution
            except EducationalInstitution.DoesNotExist:
                raise serializers.ValidationError(
                    "La institución educativa no existe.",
                )
        return value

    class Meta:
        model = Contact
        fields = [
            "first_name",
            "last_name",
            "phone_number",
            "email",
            "educational_institution",
        ]

    def create(self, validated_data):
        phone_number = validated_data.get("phone_number")

        # Generar un username único basado en el número de teléfono
        base_username = phone_number
        username = base_username
        counter = 1

        # Verificar si el username ya existe y generar uno único si es necesario
        while Contact.objects.filter(username=username).exists():
            username = f"{base_username}_{counter}"
            counter += 1

        validated_data["username"] = username
        validated_data["is_active"] = False

        return super().create(validated_data)


class CrmUpdateContactSerializer(CrmBaseContactSerializer):
    class Meta(CrmBaseContactSerializer.Meta):
        fields = CrmBaseContactSerializer.Meta.fields + [
            "first_name",
            "last_name",
            "email",
            "phone_number",
            "educational_institution",
            "ocupation",
            "company",
            "role",
            "major",
            "term",
        ]

    def validate_phone_number(self, value):
        if not value:
            return value

        # Obtener el objeto actual que estamos actualizando
        instance = self.instance

        # Si el número no ha cambiado, no necesitamos validar su unicidad
        if instance and instance.phone_number == value:
            return value

        # Si el número existe pero está eliminado
        if Contact.objects.filter(phone_number=value, deleted=True).exists():
            raise serializers.ValidationError(
                "El número de teléfono ya existe pero está eliminado. "
                "Por favor, contacta al administrador para restaurarlo.",
            )
        # Si el número existe y no está eliminado
        if Contact.objects.filter(phone_number=value, deleted=False).exists():
            raise serializers.ValidationError(
                "El número de teléfono ya existe. "
                "Por favor, utiliza otro número de teléfono.",
            )
        return value

    def validate_email(self, value):
        if not value:
            return value

        # Obtener el objeto actual que estamos actualizando
        instance = self.instance

        # Si el email no ha cambiado, no necesitamos validar su unicidad
        if instance and instance.email == value:
            return value

        # Si el email existe pero está eliminado
        if Contact.objects.filter(email=value, deleted=True).exists():
            raise serializers.ValidationError(
                "El correo electrónico ya existe pero está eliminado. "
                "Por favor, contacta al administrador para restaurarlo.",
            )
        # Si el email existe y no está eliminado
        if Contact.objects.filter(email=value, deleted=False).exists():
            raise serializers.ValidationError(
                "El correo electrónico ya existe. "
                "Por favor, utiliza otro correo electrónico.",
            )
        return value
