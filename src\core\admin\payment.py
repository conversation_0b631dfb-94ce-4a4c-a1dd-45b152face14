from django.contrib import admin
from core.models import Payment


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    readonly_fields = [
        "created_at",
        "updated_at",
        "deleted_at",
        "deleted_by",
    ]

    list_display = [
        "order",
        "amount",
        "currency",
        "payment_date",
        "created_at",
        "updated_at",
        "deleted",
    ]
    list_filter = ("deleted",)
    search_fields = ("order",)
