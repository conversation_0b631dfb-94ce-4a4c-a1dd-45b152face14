import json
from core.models import (
    Event,
    Partnership,
    EventSchedule,
    Offering,
    EventScheduleEnrollment,
    User,
)
from datetime import datetime


def convert_timestamp_to_datetime(timestamp_ms):
    """Convert timestamp in milliseconds to datetime object"""
    return datetime.fromtimestamp(timestamp_ms / 1000)


POPULATE_CRM_ASSETS_DIR = "api/crm/services/populate/assets"

events_json_file_path = f"{POPULATE_CRM_ASSETS_DIR}/14_events.json"
event_schedules_json_file_path = f"{POPULATE_CRM_ASSETS_DIR}/15_event_schedules.json"
event_schedule_partnerships_json_file_path = (
    f"{POPULATE_CRM_ASSETS_DIR}/18_event_schedule_partnerships.json"
)
event_schedule_enrollments_json_file_path = (
    f"{POPULATE_CRM_ASSETS_DIR}/16_enrollments.json"
)


def populate_event_data():
    with open(events_json_file_path) as f:
        data = json.load(f)

        events = [
            Event(
                eid=item["eid"],
                slug=item["code"],
                name=item["name"],
                description=item.get("description") or "",
                type=item["type"],
                offering=(
                    Offering.objects.get(oid=item["offering"])
                    if item.get("offering")
                    else None
                ),
            )
            for item in data
        ]

        Event.objects.bulk_create(events)


def populate_event_schedule_data():
    with open(event_schedules_json_file_path, "r", encoding="utf-8-sig") as f:
        data = json.load(f)

        event_schedules = [
            EventSchedule(
                esid=item["esid"],
                event=Event.objects.get(eid=item["event"]),
                is_general=item["is_general"],
                start_date=convert_timestamp_to_datetime(item["start_time"]),
                end_date=convert_timestamp_to_datetime(item["end_time"]),
                name=item["name"],
                description=item.get("description") or "",
                stage=item["stage"],
                ext_event_link=item.get("google_meet_link", ""),
                ext_event_reference=item.get("calendar_id", ""),
            )
            for item in data
        ]

        EventSchedule.objects.bulk_create(event_schedules)

    # Now populate partnerships for event schedules
    with open(
        event_schedule_partnerships_json_file_path, "r", encoding="utf-8-sig"
    ) as f:
        data = json.load(f)

        # data has an object with key (esid) and value is a list of partnership id (pid)
        for esid, partnerships in data.items():
            event_schedule = EventSchedule.objects.get(esid=esid)
            for pid in partnerships:
                partnership = Partnership.objects.get(pid=pid)
                event_schedule.partnerships.add(partnership)
            event_schedule.save()


def populate_event_schedule_enrollment_data():
    with open(
        event_schedule_enrollments_json_file_path, "r", encoding="utf-8-sig"
    ) as f:
        data = json.load(f)

        enrollments = []
        for item in data:
            enrollment = EventScheduleEnrollment(
                event_schedule=EventSchedule.objects.get(
                    esid=item["event_schedule_id"]
                ),
                user=(
                    User.objects.get(uid=item["contact"])
                    if item.get("contact")
                    else None
                ),
                first_name=item.get("first_name", ""),
                last_name=item.get("last_name", ""),
                email=item.get("email", ""),
                phone_number=item.get("whatsapp", ""),
                occupation=item.get("occupation", ""),
                major=item.get("university_major", ""),
                term=item.get("cycle", ""),
                university=item.get("university", ""),
                interests=item.get("specialization_interest", []),
                diffusion_channel=item.get("media", ""),
                has_contact=False,
                partnership=(
                    Partnership.objects.get(pid=item["partnership"])
                    if item.get("partnership")
                    else None
                ),
            )
            enrollments.append(enrollment)

        EventScheduleEnrollment.objects.bulk_create(enrollments)
