from rest_framework import viewsets
from core.models import Event
from api.mixins import SwaggerTagMixin
from api.website.serializers.event import WebsiteEventSerializer
from api.paginations import StandardResultsPagination


class WebsiteEventViewSet(
    SwaggerTagMixin,
    viewsets.ReadOnlyModelViewSet,
):
    model_class = Event
    serializer_class = WebsiteEventSerializer
    queryset = Event.objects.filter(deleted=False).order_by("-created_at")
    pagination_class = StandardResultsPagination

    lookup_field = "eid"

    swagger_tags = ["Website Event"]

    def get_queryset(self):
        # Return only if the event has EventSchedules is general and not be null
        # also it's on stage Launched or Enrollment closed
        return self.queryset.filter(
            schedules__isnull=False,
            schedules__is_general=True,
            schedules__stage__in=[
                Event.LAUNCHED_STAGE,
                Event.ENROLLMENT_CLOSED_STAGE,
            ],
        ).distinct()
