import uuid
from django.db import models
from .base import AuditBaseModel


class Order(AuditBaseModel):
    PROSPECT_STAGE = "prospect"
    INTERESTED_STAGE = "interested"
    TO_PAY_STAGE = "to_pay"
    SOLD_STAGE = "sold"
    LOST_STAGE = "lost"

    STAGE_CHOICES = [
        (PROSPECT_STAGE, "Prospect"),
        (INTERESTED_STAGE, "Interested"),
        (TO_PAY_STAGE, "To Pay"),
        (SOLD_STAGE, "Sold"),
        (LOST_STAGE, "Lost"),
    ]
    oid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    stage = models.CharField(
        max_length=50,
        blank=False,
        default=PROSPECT_STAGE,
        choices=STAGE_CHOICES,
        verbose_name="Stage",
    )
    benefits = models.ManyToManyField(
        "Benefit",
        blank=True,
        related_name="orders",
        verbose_name="Benefits",
    )
    lead_sources = models.ManyToManyField(
        "LeadSource",
        blank=True,
        related_name="orders",
        verbose_name="Lead Sources",
    )
    prospect_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name="Prospect At",
    )
    interested_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name="Interested At",
    )
    to_pay_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name="To Pay At",
    )
    sold_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name="Sold At",
    )
    lost_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name="Lost At",
    )
    owner = models.ForeignKey(
        "User",
        on_delete=models.CASCADE,
        related_name="orders",
        verbose_name="Owner",
    )
    sales_agent = models.ForeignKey(
        "User",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
        related_name="sales_agent",
        verbose_name="Sales Agent",
    )
    is_international = models.BooleanField(
        default=False,
        verbose_name="Is International",
    )
    has_full_scholarship = models.BooleanField(
        default=False,
        verbose_name="Is Full Scholarship",
    )

    class Meta:
        verbose_name = "Order"
        verbose_name_plural = "Orders"

    def __str__(self):
        return str(self.oid)


class OrderItem(AuditBaseModel):
    order = models.ForeignKey(
        "Order",
        on_delete=models.CASCADE,
        related_name="items",
        verbose_name="Order",
    )
    offering = models.ForeignKey(
        "Offering",
        on_delete=models.CASCADE,
        related_name="order_items",
        verbose_name="Offering",
    )
    quantity = models.PositiveIntegerField(
        default=1,
        verbose_name="Quantity",
    )
    custom_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name="Custom Amount",
        help_text="Custom amount for this order item. If not set, the offering price will be used.",
    )

    @property
    def base_price(self):
        return self.offering.base_price

    @property
    def foreign_base_price(self):
        return self.offering.foreign_base_price

    @property
    def discount(self):
        return self.offering.discount

    @property
    def unit_price(self):
        return self.offering.price

    @property
    def foreign_unit_price(self):
        return self.offering.foreign_price

    @property
    def total_price(self):
        return self.unit_price * self.quantity

    @property
    def foreign_total_price(self):
        return self.foreign_price * self.quantity

    @property
    def has_custom_amount(self):
        return self.custom_amount is not None

    @property
    def custom_amount_price(self):
        if self.has_custom_amount:
            return self.custom_amount * self.quantity
        return None

    class Meta:
        verbose_name = "Order Item"
        verbose_name_plural = "Order Items"
        unique_together = ["order", "offering"]

    def __str__(self):
        return f"{self.order.oid} - {self.offering.name}"


class Benefit(AuditBaseModel):
    bid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    name = models.CharField(
        max_length=255,
        blank=False,
        verbose_name="Name",
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name="Description",
    )

    class Meta:
        verbose_name = "Benefit"
        verbose_name_plural = "Benefits"

    def __str__(self):
        return self.name


class LeadSource(AuditBaseModel):
    lsid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    name = models.CharField(
        max_length=255,
        blank=False,
        verbose_name="Name",
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name="Description",
    )

    class Meta:
        verbose_name = "Lead Source"
        verbose_name_plural = "Lead Sources"

    def __str__(self):
        return self.name
