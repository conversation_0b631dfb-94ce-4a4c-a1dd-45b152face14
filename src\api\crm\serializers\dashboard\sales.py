"""
Sales Dashboard Serializers for CRM
Provides serialization for sales dashboard analytics data
"""

from rest_framework import serializers
from core.models import Order, User, Offering
from api.crm.serializers.order import CrmOrderSerializer

class CrmDashboardSalesOwnerSerializer(serializers.ModelSerializer):
    """Serializer for order owner information"""
    
    full_name = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            "uid",
            "first_name", 
            "last_name",
            "full_name",
            "email",
            "phone_number"
        ]
    
    def get_full_name(self, obj):
        return obj.get_full_name() if obj.get_full_name() != "" else "Sin nombre"


class CrmDashboardSalesAgentSerializer(serializers.ModelSerializer):
    """Serializer for sales agent information"""
    
    full_name = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            "uid",
            "first_name",
            "last_name", 
            "full_name"
        ]
    
    def get_full_name(self, obj):
        return obj.get_full_name() if obj.get_full_name() != "" else "Sin nombre"



class CrmDashboardSalesFilterOptionsSerializer(serializers.Serializer):
    """Serializer for filter options"""
    
    products = serializers.SerializerMethodField()
    sales_agents = serializers.SerializerMethodField()
    
    def get_products(self, obj):
        """Get available products/offerings for filtering"""
        offerings = Offering.objects.filter(deleted=False).values(
            'oid', 'name'
        )
        return [
            {
                'oid': str(offering['oid']),
                'name': offering['name'],
                'slug': offering['name'].lower().replace(' ', '-')
            }
            for offering in offerings
        ]
    
    def get_sales_agents(self, obj):
        """Get available sales agents for filtering"""
        agents = User.objects.filter(
            deleted=False,
            is_staff=True,
            sales_agent__isnull=False
        ).distinct().values('uid', 'first_name', 'last_name')
        
        return [
            {
                'uid': str(agent['uid']),
                'full_name': f"{agent['first_name']} {agent['last_name']}".strip() or "Sin nombre"
            }
            for agent in agents
        ]


class CrmDashboardSalesSerializer(serializers.Serializer):
    """Main serializer for sales dashboard response"""
    
    stats = serializers.DictField()
    conversion_funnel = serializers.ListField()
    conversion_by_sale_stages = serializers.DictField()
    orders_by_month = serializers.ListField()
    orders_by_stage = serializers.ListField()
    revenue_by_month = serializers.ListField()
    revenue_by_sales_agent = serializers.ListField()
    top_selling_products = serializers.ListField()
    current_month_performance = serializers.DictField()
    recent_orders = CrmOrderSerializer(many=True)
    filter_options = CrmDashboardSalesFilterOptionsSerializer()
