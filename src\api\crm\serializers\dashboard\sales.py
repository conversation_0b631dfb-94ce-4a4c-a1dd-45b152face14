"""
Sales Dashboard Serializers for CRM
Provides serialization for sales dashboard analytics data
"""

from rest_framework import serializers
from core.models import Order, User, Offering


class CrmDashboardSalesOwnerSerializer(serializers.ModelSerializer):
    """Serializer for order owner information"""
    
    full_name = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            "uid",
            "first_name", 
            "last_name",
            "full_name",
            "email",
            "phone_number"
        ]
    
    def get_full_name(self, obj):
        return obj.get_full_name() if obj.get_full_name() != "" else "Sin nombre"


class CrmDashboardSalesAgentSerializer(serializers.ModelSerializer):
    """Serializer for sales agent information"""
    
    full_name = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            "uid",
            "first_name",
            "last_name", 
            "full_name"
        ]
    
    def get_full_name(self, obj):
        return obj.get_full_name() if obj.get_full_name() != "" else "Sin nombre"


class CrmDashboardSalesOrderItemSerializer(serializers.ModelSerializer):
    """Serializer for order items in dashboard context"""
    
    offering_name = serializers.CharField(source="offering.name", read_only=True)
    offering_oid = serializers.CharField(source="offering.oid", read_only=True)
    
    class Meta:
        model = Order
        fields = [
            "offering_name",
            "offering_oid",
            "quantity",
            "effective_total_price"
        ]


class CrmDashboardSalesRecentOrderSerializer(serializers.ModelSerializer):
    """Serializer for recent orders in dashboard"""
    
    owner = CrmDashboardSalesOwnerSerializer(read_only=True)
    sales_agent = CrmDashboardSalesAgentSerializer(read_only=True)
    order_items = serializers.SerializerMethodField()
    total_amount = serializers.SerializerMethodField()
    currency = serializers.SerializerMethodField()
    
    class Meta:
        model = Order
        fields = [
            "oid",
            "owner",
            "stage", 
            "total_amount",
            "currency",
            "created_at",
            "updated_at",
            "prospect_at",
            "interested_at",
            "to_pay_at",
            "sold_at",
            "sales_agent",
            "order_items"
        ]
    
    def get_order_items(self, obj):
        """Get simplified order items data"""
        items_data = []
        for item in obj.items.all():
            items_data.append({
                "offering_name": item.offering.name,
                "offering_oid": str(item.offering.oid),
                "quantity": item.quantity,
                "effective_total_price": float(item.effective_total_price)
            })
        return items_data
    
    def get_total_amount(self, obj):
        """Get total amount in appropriate currency"""
        return float(sum(item.effective_total_price for item in obj.items.all()))
    
    def get_currency(self, obj):
        """Get currency based on international status"""
        return "USD" if obj.is_international else "PEN"


class CrmDashboardSalesFilterOptionsSerializer(serializers.Serializer):
    """Serializer for filter options"""
    
    products = serializers.SerializerMethodField()
    sales_agents = serializers.SerializerMethodField()
    
    def get_products(self, obj):
        """Get available products/offerings for filtering"""
        offerings = Offering.objects.filter(deleted=False).values(
            'oid', 'name'
        )
        return [
            {
                'oid': str(offering['oid']),
                'name': offering['name'],
                'slug': offering['name'].lower().replace(' ', '-')
            }
            for offering in offerings
        ]
    
    def get_sales_agents(self, obj):
        """Get available sales agents for filtering"""
        agents = User.objects.filter(
            deleted=False,
            is_staff=True,
            sales_agent__isnull=False
        ).distinct().values('uid', 'first_name', 'last_name')
        
        return [
            {
                'uid': str(agent['uid']),
                'full_name': f"{agent['first_name']} {agent['last_name']}".strip() or "Sin nombre"
            }
            for agent in agents
        ]


class CrmDashboardSalesSerializer(serializers.Serializer):
    """Main serializer for sales dashboard response"""
    
    stats = serializers.DictField()
    conversion_funnel = serializers.ListField()
    conversion_by_sale_stages = serializers.DictField()
    orders_by_month = serializers.ListField()
    orders_by_stage = serializers.ListField()
    revenue_by_month = serializers.ListField()
    revenue_by_sales_agent = serializers.ListField()
    top_selling_products = serializers.ListField()
    current_month_performance = serializers.DictField()
    recent_orders = CrmDashboardSalesRecentOrderSerializer(many=True)
    filter_options = CrmDashboardSalesFilterOptionsSerializer()
