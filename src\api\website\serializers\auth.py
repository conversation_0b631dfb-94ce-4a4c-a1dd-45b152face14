from django.core.validators import RegexValidator
from django.contrib.auth.models import Group
from rest_framework import serializers
from core.models import User
from django.contrib.auth import authenticate
from django.utils.translation import gettext_lazy as _


class WebsiteAuthRegisterSerializer(serializers.ModelSerializer):
    password = serializers.CharField(
        write_only=True,
        required=True,
        help_text="Password must contain at least 8 characters, including uppercase, lowercase, numbers and special characters",
        style={"input_type": "password"},
        validators=[
            RegexValidator(
                regex=r"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$",
                message="Password must contain at least 8 characters, including uppercase, lowercase, numbers and special characters",
            ),
        ],
    )
    confirm_password = serializers.CharField(
        write_only=True,
        required=True,
        help_text="Password confirmation",
        style={"input_type": "password"},
    )
    email = serializers.EmailField(
        required=True,
        validators=[
            RegexValidator(
                regex=r"^[a-zA-Z0-9._%+-]+@gmail\.com$",
                message="Only Gmail accounts are allowed.",
            ),
        ],
    )

    class Meta:
        model = User
        fields = [
            "email",
            "password",
            "confirm_password",
            "first_name",
            "last_name",
        ]
        extra_kwargs = {
            "password": {"write_only": True},
            "confirm_password": {"write_only": True},
        }

    def create(self, validated_data):
        confirm_password = validated_data.pop("confirm_password")
        if validated_data["password"] != confirm_password:
            raise serializers.ValidationError(
                {"confirm_password": "Passwords do not match"}
            )

        username = validated_data.get("email").split("@")[0]
        user_with_same_username = User.objects.filter(username=username)

        if user_with_same_username.exists():
            username = f"{username}{user_with_same_username.count() + 1}"
        validated_data["username"] = username

        user = User.objects.create_user(**validated_data)

        try:
            students_group = Group.objects.get(name="students")
            user.groups.add(students_group)
        except Group.DoesNotExist:
            students_group = Group.objects.create(name="students")
            user.groups.add(students_group)
        return user


class WebsiteAuthLoginSerializer(serializers.Serializer):
    username = serializers.CharField(label=_("Email or username"), write_only=True)
    password = serializers.CharField(
        label=_("Password"),
        style={"input_type": "password"},
        trim_whitespace=False,
        write_only=True,
    )
    token = serializers.CharField(label=_("Token"), read_only=True)

    def validate(self, attrs):
        username = attrs.get("username")
        password = attrs.get("password")

        if username and "@" in username:
            user = User.objects.filter(
                email=username,
                is_active=True,
                groups__name__in=["students", "teachers"],
            ).first()

            if not user:
                msg = _(
                    "User with this email does not exist or does not have the required permissions."
                )
                raise serializers.ValidationError(msg, code="authorization")
            else:
                username = user.username

        if username and password:
            user = authenticate(
                request=self.context.get("request"),
                username=username,
                password=password,
            )

            if not user:
                msg = _("Unable to log in with provided credentials.")
                raise serializers.ValidationError(msg, code="authorization")

            if not user.groups.filter(name__in=["students", "teachers"]).exists():
                msg = _("Access restricted to students and teachers only.")
                raise serializers.ValidationError(msg, code="authorization")
        else:
            msg = _('Must include "username" and "password".')
            raise serializers.ValidationError(msg, code="authorization")

        attrs["user"] = user
        return attrs
