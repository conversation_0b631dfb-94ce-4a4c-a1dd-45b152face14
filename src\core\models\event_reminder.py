import uuid
from django.db import models
from core.models.base import AuditBaseModel


class EventReminder(AuditBaseModel):
    DRAFT = "DRAFT"
    PENDING = "PENDING"
    SENT = "SENT"
    FAILED = "FAILED"

    STATUS_CHOICES = [
        (DRAFT, "Draft"),
        (PENDING, "Pending"),
        (SENT, "Sent"),
        (FAILED, "Failed"),
    ]

    M0 = "M0"
    M1 = "M1"
    M2 = "M2"
    M3 = "M3"

    REMINDER_TYPES = [
        (M0, "M0"),
        (M1, "M1"),
        (M2, "M2"),
        (M3, "M3"),
    ]

    rid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    reminder_type = models.CharField(
        max_length=2,
        choices=REMINDER_TYPES,
        default=M0,
        verbose_name="Reminder Type",
    )
    event_alliance_id = models.IntegerField()
    event_name = models.CharField(max_length=255, blank=False, verbose_name="Event Name")
    template_id = models.ForeignKey(
        "Template",
        on_delete=models.CASCADE,
        related_name="reminders",
        verbose_name="Message Template",
    )
    variables = models.JSONField(
        blank=True,
        null=True,
        verbose_name="Template Variables",
    )
    send_at = models.DateTimeField(verbose_name="Scheduled Send Time")
    status = models.CharField(
        max_length=10,
        choices=STATUS_CHOICES,
        default=DRAFT,
        verbose_name="Status",
    )

    def __str__(self):
        return f"Reminder for {self.event_name} at {self.send_at}"

    class Meta:
        verbose_name = "Event Reminder"
        verbose_name_plural = "Event Reminders"
