from storage.minio import MinioStorage


def delete_file_from_bucket(bucket_name: str, object_name: str) -> None:
    storage = MinioStorage()
    storage.remove(
        bucket_name=bucket_name,
        object_name=object_name,
    )


def get_object_size(bucket_name: str, object_name: str) -> int:
    storage = MinioStorage()
    return storage.stat_object(
        bucket_name=bucket_name,
        object_name=object_name,
    ).size


def get_object_content_type(bucket_name: str, object_name: str) -> str:
    storage = MinioStorage()
    return storage.stat_object(
        bucket_name=bucket_name,
        object_name=object_name,
    ).content_type


def get_presigned_url(bucket_name: str, object_name: str) -> str:
    storage = MinioStorage()
    return storage.get_presigned_url(
        bucket_name=bucket_name,
        object_name=object_name,
    )
