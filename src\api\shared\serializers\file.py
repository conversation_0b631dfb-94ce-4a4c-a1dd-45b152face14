from rest_framework import serializers
from core.models import File


class FileSerializer(serializers.ModelSerializer):
    url = serializers.URLField(read_only=True)
    content_type = serializers.CharField(read_only=True)

    class Meta:
        model = File
        fields = [
            "fid",
            "name",
            "url",
            "content_type",
            "is_used",
            "width",
            "height",
            "description",
        ]
        read_only_fields = fields


class FileUploadSerializer(serializers.ModelSerializer):
    file = serializers.FileField(write_only=True)
    output_format = serializers.ChoiceField(
        choices=["WEBP", "JPEG", "PNG"], default="WEBP", required=False
    )

    class Meta:
        model = File
        fields = ["file", "width", "height", "description", "is_used", "output_format"]


class FileUpdateSerializer(serializers.ModelSerializer):
    file = serializers.FileField(write_only=True, required=False)
    output_format = serializers.ChoiceField(
        choices=["WEBP", "JPEG", "PNG"], default="WEBP", required=False
    )

    class Meta:
        model = File
        fields = ["file", "width", "height", "description", "is_used", "output_format"]
