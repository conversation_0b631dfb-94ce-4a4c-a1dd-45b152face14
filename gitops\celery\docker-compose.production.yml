services:
  celery:
    build:
      context: ../../
      dockerfile: gitops/celery/Dockerfile.production
    container_name: portals-celery
    command: celery -A core worker --loglevel=info
    environment:
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_PORT=${EMAIL_PORT}
      - EMAIL_USE_TLS=${EMAIL_USE_TLS}
      - EMAIL_HOST_USER=${EMAIL_HOST_USER}
      - EMAIL_HOST_PASSWORD=${EMAIL_HOST_PASSWORD}
      - DEFAULT_FROM_EMAIL=${DEFAULT_FROM_EMAIL}
    networks:
      - ceu-network

  celery-beat:
    build:
      context: ../../
      dockerfile: gitops/celery/Dockerfile.production
    container_name: portals-celery-beat
    command: celery -A core beat --loglevel=info
    networks:
      - ceu-network

networks:
  ceu-network:
    external: true
