import uuid
from django.db import models
from .base import AuditBaseModel
from django.utils.translation import gettext_lazy as _


class EducationalInstitution(AuditBaseModel):
    UNIVERSITY_PUBLIC = "university_public"
    UNIVERSITY_PRIVATE = "university_private"
    INSTITUTE_PUBLIC = "institute_public"
    INSTITUTE_PRIVATE = "institute_private"
    COLLEGE_PUBLIC = "college_public"
    COLLEGE_PRIVATE = "college_private"

    INSTITUTION_TYPE_CHOICES = [
        (UNIVERSITY_PUBLIC, "Public University"),
        (UNIVERSITY_PRIVATE, "Private University"),
        (INSTITUTE_PUBLIC, "Public Institute"),
        (INSTITUTE_PRIVATE, "Private Institute"),
        (COLLEGE_PUBLIC, "Public College"),
        (COLLEGE_PRIVATE, "Private College"),
    ]

    eiid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )

    name = models.CharField(
        max_length=128,
        unique=True,
        help_text=_("Name of the educational institution"),
    )

    country = models.CharField(
        max_length=64,
        null=True,
        blank=True,
        help_text=_("Country where the institution is located"),
    )

    region = models.CharField(
        max_length=128,
        null=True,
        blank=True,
        help_text=_("Region where the institution is located"),
    )

    city = models.CharField(
        max_length=128,
        null=True,
        blank=True,
        help_text=_("City where the institution is located"),
    )

    acronym = models.CharField(
        max_length=32,
        null=True,
        blank=True,
        help_text=_("Acronym of the institution"),
    )

    institution_type = models.CharField(
        max_length=64,
        null=True,
        blank=True,
        choices=INSTITUTION_TYPE_CHOICES,
        help_text=_("Type of educational institution"),
    )

    class Meta:
        verbose_name = "Educational Institution"
        verbose_name_plural = "Educational Institutions"


class Partnership(AuditBaseModel):
    """Partnership model for educational institutions"""

    pid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text=_("Unique identifier for the partnership"),
    )

    name = models.CharField(
        max_length=128,
        help_text=_("Name of the partnership"),
    )

    long_name = models.CharField(
        max_length=256,
        null=True,
        blank=True,
        help_text=_("Long name of the partnership"),
    )

    description = models.TextField(
        null=True,
        blank=True,
        help_text=_("Description of the partnership"),
    )

    institution = models.ForeignKey(
        EducationalInstitution,
        on_delete=models.CASCADE,
        related_name="partnerships",
        help_text=_("Educational institution involved in the partnership"),
    )
    delegate = models.ForeignKey(
        "User",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="partnerships",
        help_text=_("User who is the delegate for the partnership"),
    )

    class Meta:
        verbose_name = "Partnership"
        verbose_name_plural = "Partnerships"
