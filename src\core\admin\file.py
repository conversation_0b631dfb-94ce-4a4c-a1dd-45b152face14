from django.utils.html import format_html
from django.contrib import admin
from core.models import File


@admin.register(File)
class FileAdmin(admin.ModelAdmin):
    readonly_fields = [
        "url_link",
        "created_at",
        "updated_at",
        "deleted_at",
        "deleted_by",
    ]

    list_display = [
        "fid",
        "name",
        "is_used",
        "format",
        "content_type",
        "size",
        "url",
        "created_at",
        "updated_at",
        "deleted",
    ]
    list_filter = ("deleted",)
    search_fields = ("name",)
    ordering = ("-created_at",)

    def url_link(self, obj):
        if obj.url:
            return format_html('<a href="{}" target="_blank">{}</a>', obj.url, obj.url)
        return "-"

    url_link.short_description = "Source URL"
