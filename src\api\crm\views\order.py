from rest_framework import viewsets
from rest_framework import filters
from rest_framework.decorators import action
from core.models import Order, OrderItem
from api.crm.serializers.order import (
    CrmOrderSerializer,
    CrmCreateOrderSerializer,
    CrmRetrieveOrderSerializer,
    CrmUpdateOrderSerializer,
    CrmOrderItemCreateSerializer,
    CrmOrderItemUpdateSerializer,
    CrmOrderItemSerializer,
)
from api.paginations import StandardResultsPagination
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.permissions import IsStaffUser
from api.mixins import AuditMixin, SwaggerTagMixin
from rest_framework.response import Response
from rest_framework import status
from django_filters.rest_framework import DjangoFilterBackend


class CrmOrderViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = Order
    queryset = Order.objects.filter(deleted=False).order_by("-created_at")
    serializer_class = CrmOrderSerializer
    swagger_tags = ["Order"]
    pagination_class = StandardResultsPagination

    # authentication_classes = [TokenAuthentication]
    # permission_classes = [IsAuthenticated & IsStaffUser]

    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = [
        "owner",
    ]
    search_fields = [
        "owner__first_name",
        "owner__last_name",
        "owner__email",
        "owner__phone_number",
    ]
    # ordering_fields = ["name", "created_at", "updated_at", "price"]

    def get_serializer(self, *args, **kwargs):
        if self.action == "create":
            return CrmCreateOrderSerializer(
                *args, context=self.get_serializer_context(), **kwargs
            )
        elif self.action == "retrieve":
            return CrmRetrieveOrderSerializer(*args, **kwargs)
        elif self.action in ["update", "partial_update"]:
            return CrmUpdateOrderSerializer(
                *args, context=self.get_serializer_context(), **kwargs
            )
        elif self.action == "update_item":
            return CrmOrderItemUpdateSerializer(
                *args, context=self.get_serializer_context(), **kwargs
            )
        return super().get_serializer(*args, **kwargs)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        retrieve_serializer = CrmRetrieveOrderSerializer(serializer.instance)
        return Response(retrieve_serializer.data, status=status.HTTP_201_CREATED)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        retrieve_serializer = CrmRetrieveOrderSerializer(serializer.instance)
        return Response(retrieve_serializer.data)

    @action(detail=True, methods=["POST"], url_path="item")
    def add_item(self, request, pk=None):
        """
        Add an item to the order
        """
        order = self.get_object()
        serializer = CrmOrderItemCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(order=order)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=["PATCH"], url_path="item/(?P<item_id>[^/.]+)")
    def update_item(self, request, pk=None, item_id=None):
        """
        Update a specific order item (quantity, custom_amount)
        """

        order = self.get_object()

        try:
            item = OrderItem.objects.get(id=item_id, order=order)
        except OrderItem.DoesNotExist:
            return Response(
                {"error": "Order item not found"}, status=status.HTTP_404_NOT_FOUND
            )

        serializer = CrmOrderItemUpdateSerializer(item, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        response_serializer = CrmOrderItemSerializer(item)
        return Response(response_serializer.data)
