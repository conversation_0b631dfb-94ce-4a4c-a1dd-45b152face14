from rest_framework import viewsets, filters, status
from rest_framework.response import Response
from core.models import BlogCategory
from api.cms.serializers.blog_category import (
    CmsBlogCategorySerializer,
    CmsCreateBlogCategorySerializer,
    CmsUpdateBlogCategorySerializer,
)
from api.cms.filters.blog_category import CmsBlogCategoryFilter
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import Is<PERSON><PERSON>ent<PERSON><PERSON>, IsAdminUser, AllowAny
from api.mixins import AuditMixin, SwaggerTagMixin
from django_filters.rest_framework import DjangoFilterBackend
from api.paginations import StandardResultsPagination


class CmsBlogCategoryViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsAdminUser]

    model_class = BlogCategory
    queryset = BlogCategory.objects.filter(deleted=False).order_by("name")
    serializer_class = CmsBlogCategorySerializer

    pagination_class = StandardResultsPagination

    filterset_class = CmsBlogCategoryFilter
    filter_backends = (DjangoFilterBackend, filters.OrderingFilter)
    ordering_fields = ["name", "created_at"]

    swagger_tags = ["Blog Categories"]

    def get_serializer_class(self):
        if self.action == "create":
            return CmsCreateBlogCategorySerializer
        if self.action in ["update", "partial_update"]:
            return CmsUpdateBlogCategorySerializer
        return super().get_serializer_class()

    def destroy(self, request, *args, **kwargs):
        """
        Override to validate if the category is in use before deletion.
        """
        instance = self.get_object()

        # Check if the category is being used by any non-deleted blog posts
        if instance.blog_posts.filter(deleted=False).exists():
            return Response(
                "Esta categoría está en uso por un blog",
                status=status.HTTP_400_BAD_REQUEST
            )

        # Call the parent class's destroy method to mark the category as deleted
        return super().destroy(request, *args, **kwargs)
