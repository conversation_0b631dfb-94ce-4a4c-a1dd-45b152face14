import uuid
from django.db import models
from core.models.base import AuditBaseModel


class Template(AuditBaseModel):
    DRAFT = "DRAFT"
    IN_REVIEW = "IN_REVIEW"
    REJECTED = "REJECTED"
    APPROVED = "APPROVED"
    PAUSED = "PAUSED"
    DISABLED = "DISABLED"

    STATUS_CHOICES = [
        (DRAFT, "Draft"),
        (IN_REVIEW, "In Review"),
        (REJECTED, "Rejected"),
        (APPROVED, "Approved"),
        (PAUSED, "Paused"),
        (DISABLED, "Disabled"),
    ]

    tid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    name = models.CharField(max_length=255, blank=False, verbose_name="Template Name")
    status = models.CharField(
        max_length=100,
        choices=STATUS_CHOICES,
        default=DRAFT,
        verbose_name="Status",
    )
    header_image = models.ForeignKey(
        "File",
        on_delete=models.SET_NULL,
        related_name="templates",
        blank=True,
        null=True,
        verbose_name="Header Image",
    )
    header_image_meta_url = models.TextField(blank=True, null=True, verbose_name="Header Image Meta URL")
    body_text = models.TextField(blank=False, verbose_name="Body Text", max_length=1024)
    
    positional_params_example = models.JSONField(blank=True, null=True, verbose_name="Positional Parameters Example")

    buttons = models.JSONField(blank=True, null=True, verbose_name="Buttons")
    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Message Template"
        verbose_name_plural = "Message Templates"
