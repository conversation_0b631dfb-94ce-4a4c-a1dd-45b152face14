# CEU Portals

This is the README file for the project "CEU Portals". 

## Description

CEU Portals is the core of an integral platform for manage "CEU Centro de Especialización". This repository pretends to be the backend project with Django Rest Framework.

## Installation

1. First you need to allocate the required services like a DB and Bucket. Go to installation directory and deploy locally the docker-compose services. With `make resources` cli command.
2. Then you need to configure an isolate python environment with conda or venv. Then run `make install` command for install the necesary libraries.
3. Then initialize a environment with `make env`.

## Usage

For first usage you need to apply the migrate and create super user commands from django manage script.

You can do this with:

- `make migrate`
- `make create-superuser`

Then for start the development server run:

- `make run-api`

## Team

Tech Lead
@reqhiem 