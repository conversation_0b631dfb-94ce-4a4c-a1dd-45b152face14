services:
  ceu-storage:
    image: minio/minio
    container_name: ceu-storage
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: ""
    command: server /data --console-address ":9001"
    volumes:
      - minio-data:/data
    restart: always
    networks:
      - ceu-network

volumes:
  minio-data:

networks:
  ceu-network:
    external: true
