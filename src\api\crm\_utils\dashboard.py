"""
Dashboard utilities for CRM
Shared functions for dashboard calculations and data processing
"""

from django.utils import timezone
from django.db.models import Q
import requests
from decimal import Decimal
from django.conf import settings


class DashboardUtils:
    """Utility class for dashboard calculations"""
    
    @staticmethod
    def get_report_dates(request):
        """
        Get current and previous period dates based on created_at filters
        If no filters provided, defaults to current month vs previous month
        """
        created_at_after = request.GET.get("created_at_after")  # YYYY-MM-DD
        created_at_before = request.GET.get("created_at_before")  # YYYY-MM-DD

        def get_current_week():
            # current week from monday to sunday
            current_end = timezone.now()
            current_start = current_end - timezone.timedelta(
                days=current_end.weekday()
            )  # Lunes de esta semana
            current_start = current_start.replace(
                hour=0, minute=0, second=0, microsecond=0
            )

            # Previous week (7 days before)
            previous_end = current_start - timezone.timedelta(days=1)
            previous_start = previous_end - timezone.timedelta(days=6)  # 7 días atrás
            previous_start = previous_start.replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            previous_end = previous_end.replace(
                hour=23, minute=59, second=59, microsecond=999999
            )
            return current_start, current_end, previous_start, previous_end

        if created_at_after and created_at_before:
            try:
                # Parse dates from filters
                current_start = timezone.datetime.strptime(created_at_after, "%Y-%m-%d")
                current_end = timezone.datetime.strptime(created_at_before, "%Y-%m-%d")

                # Make timezone aware
                current_start = timezone.make_aware(
                    current_start.replace(hour=0, minute=0, second=0, microsecond=0)
                )
                current_end = timezone.make_aware(
                    current_end.replace(
                        hour=23, minute=59, second=59, microsecond=999999
                    )
                )

                # Calculate period duration in days
                period_duration = (current_end.date() - current_start.date()).days + 1

                # Calculate previous period (same duration, ending the day before current start)
                previous_end = current_start - timezone.timedelta(days=1)
                previous_start = previous_end - timezone.timedelta(
                    days=period_duration - 1
                )
                previous_start = previous_start.replace(
                    hour=0, minute=0, second=0, microsecond=0
                )
                previous_end = previous_end.replace(
                    hour=23, minute=59, second=59, microsecond=999999
                )

            except (ValueError, TypeError):
                # Fallback to current week if invalid format
                current_start, current_end, previous_start, previous_end = (
                    get_current_week()
                )
        else:
            # No date filters provided - default to current week vs previous week
            current_start, current_end, previous_start, previous_end = (
                get_current_week()
            )

        return {
            "current_start": current_start,
            "current_end": current_end,
            "previous_start": previous_start,
            "previous_end": previous_end,
            "period_days": (current_end.date() - current_start.date()).days + 1,
            "current_month": current_start.strftime(
                "%Y-%m"
            ),  # use current start to calculate current month
        }

    @staticmethod
    def get_queryset_excluding_filters(queryset, filterset_class, request, exclude_fields):
        """
        Get queryset, applies all filters EXCEPT specified fields
        """
        filter_params = request.GET.copy()

        if isinstance(exclude_fields, str):
            exclude_fields = [exclude_fields]

        for field in exclude_fields:
            if field == "created_at":
                # Remove date range filters
                filter_params.pop("created_at_after", None)
                filter_params.pop("created_at_before", None)
            else:
                filter_params.pop(field, None)

        # Apply remaining filters
        filterset = filterset_class(filter_params, queryset=queryset)
        return filterset.qs if filterset.is_valid() else queryset

    @staticmethod
    def calculate_percentage_change(current, previous):
        """Calculate percentage change between two values"""
        if previous == 0:
            if current > 0:
                return 100.0, "up"
            else:
                return 0.0, "flat"
        
        percentage = ((current - previous) / previous) * 100
        
        if percentage > 0:
            tendency = "up"
        elif percentage < 0:
            tendency = "down"
        else:
            tendency = "flat"
            
        return round(percentage, 1), tendency

    @staticmethod
    def get_month_names():
        """Get Spanish month names for charts"""
        return [
            "ene", "feb", "mar", "abr", "may", "jun",
            "jul", "ago", "sep", "oct", "nov", "dic"
        ]


class CurrencyConverter:
    """Utility class for currency conversion"""

    @staticmethod
    def usd_to_pen(amount_usd):
        """
        Convert USD to PEN using Exchange Rate API
        Returns the amount in PEN or the original amount if conversion fails
        """
        if not amount_usd or amount_usd == 0:
            return Decimal('0.00')

        try:
            # Exchange Rate API endpoint
            api_key = getattr(settings, 'EXCHANGE_RATE_API_KEY', None)
            if not api_key:
                # Fallback to a fixed rate if no API key
                return amount_usd * Decimal('3.75')  # Approximate rate

            url = f"https://v6.exchangerate-api.com/v6/{api_key}/pair/USD/PEN"
            response = requests.get(url, timeout=5)

            if response.status_code == 200:
                data = response.json()
                if data.get('result') == 'success':
                    rate = Decimal(str(data['conversion_rate']))
                    return amount_usd * rate
                else:
                    # Fallback rate
                    return amount_usd * Decimal('3.75')
            else:
                # Fallback rate
                return amount_usd * Decimal('3.75')

        except Exception:
            # Fallback rate in case of any error
            return amount_usd * Decimal('3.75')

    @staticmethod
    def convert_order_total_to_pen(order):
        """
        Convert order total to PEN based on is_international flag
        Uses custom_amount for sold orders (persisted prices)
        """
        total = Decimal('0.00')

        for item in order.items.all():
            if order.stage == order.SOLD_STAGE and item.custom_amount is not None:
                # Use persisted custom_amount for sold orders
                item_total = item.custom_amount * item.quantity
            else:
                # Use effective price for non-sold orders
                item_total = item.effective_total_price

            if order.is_international:
                # Convert from USD to PEN
                total += CurrencyConverter.usd_to_pen(item_total)
            else:
                # Already in PEN
                total += item_total

        return total


class StageNameMapper:
    """Utility class for mapping stage codes to Spanish names"""

    STAGE_NAMES = {
        'prospect': 'Prospecto',
        'interested': 'Interesado',
        'to_pay': 'Por pagar',
        'sold': 'Vendido',
        'lost': 'Perdido'
    }

    @staticmethod
    def get_stage_name(stage_code):
        """Get Spanish name for stage code"""
        return StageNameMapper.STAGE_NAMES.get(stage_code, stage_code.title())


class TrendCalculator:
    """Utility class for calculating historical trends"""

    @staticmethod
    def calculate_historical_trend(queryset, field_name, current_start, current_end, periods=3):
        """
        Calculate trend based on historical data from multiple periods

        Args:
            queryset: Django queryset to analyze
            field_name: Field to filter by date (e.g., 'sold_at', 'created_at')
            current_start: Start date of current period
            current_end: End date of current period
            periods: Number of historical periods to analyze (default: 3)

        Returns:
            dict: {
                'tendency': 'up'|'down'|'flat',
                'trend_percentage': float,
                'historical_data': list of period values
            }
        """
        from django.utils import timezone

        # Calculate period duration
        period_duration = (current_end.date() - current_start.date()).days + 1

        # Get historical data for the specified number of periods
        historical_values = []

        for i in range(periods, 0, -1):  # Go backwards in time
            # Calculate period start and end
            period_end = current_start - timezone.timedelta(days=1)
            period_start = period_end - timezone.timedelta(days=period_duration - 1)
            period_start = period_start.replace(hour=0, minute=0, second=0, microsecond=0)
            period_end = period_end.replace(hour=23, minute=59, second=59, microsecond=999999)

            # Count items in this period
            period_filter = {f"{field_name}__gte": period_start, f"{field_name}__lte": period_end}
            period_count = queryset.filter(**period_filter).count()
            historical_values.append(period_count)

            # Move to previous period
            current_start = period_start

        # Calculate trend
        if len(historical_values) < 2:
            return {
                'tendency': 'flat',
                'trend_percentage': 0.0,
                'historical_data': historical_values
            }

        # Calculate average change between periods
        changes = []
        for i in range(1, len(historical_values)):
            prev_value = historical_values[i-1]
            curr_value = historical_values[i]

            if prev_value > 0:
                change = ((curr_value - prev_value) / prev_value) * 100
                changes.append(change)

        if not changes:
            return {
                'tendency': 'flat',
                'trend_percentage': 0.0,
                'historical_data': historical_values
            }

        # Calculate average trend
        avg_trend = sum(changes) / len(changes)

        # Determine tendency
        if avg_trend > 5:  # More than 5% average growth
            tendency = 'up'
        elif avg_trend < -5:  # More than 5% average decline
            tendency = 'down'
        else:
            tendency = 'flat'

        return {
            'tendency': tendency,
            'trend_percentage': round(avg_trend, 1),
            'historical_data': historical_values
        }

    @staticmethod
    def calculate_revenue_trend(orders_queryset, current_start, current_end, periods=3):
        """
        Calculate revenue trend using historical data

        Args:
            orders_queryset: Queryset of sold orders
            current_start: Start date of current period
            current_end: End date of current period
            periods: Number of historical periods to analyze

        Returns:
            dict: Trend information for revenue
        """
        from django.utils import timezone

        # Calculate period duration
        period_duration = (current_end.date() - current_start.date()).days + 1

        # Get historical revenue data
        historical_revenues = []

        for i in range(periods, 0, -1):  # Go backwards in time
            # Calculate period start and end
            period_end = current_start - timezone.timedelta(days=1)
            period_start = period_end - timezone.timedelta(days=period_duration - 1)
            period_start = period_start.replace(hour=0, minute=0, second=0, microsecond=0)
            period_end = period_end.replace(hour=23, minute=59, second=59, microsecond=999999)

            # Get orders for this period
            period_orders = orders_queryset.filter(
                sold_at__gte=period_start,
                sold_at__lte=period_end
            )

            # Calculate total revenue for this period
            period_revenue = 0
            for order in period_orders:
                period_revenue += CurrencyConverter.convert_order_total_to_pen(order)

            historical_revenues.append(float(period_revenue))

            # Move to previous period
            current_start = period_start

        # Calculate trend
        if len(historical_revenues) < 2:
            return {
                'tendency': 'flat',
                'trend_percentage': 0.0,
                'historical_data': historical_revenues
            }

        # Calculate average change between periods
        changes = []
        for i in range(1, len(historical_revenues)):
            prev_value = historical_revenues[i-1]
            curr_value = historical_revenues[i]

            if prev_value > 0:
                change = ((curr_value - prev_value) / prev_value) * 100
                changes.append(change)

        if not changes:
            return {
                'tendency': 'flat',
                'trend_percentage': 0.0,
                'historical_data': historical_revenues
            }

        # Calculate average trend
        avg_trend = sum(changes) / len(changes)

        # Determine tendency
        if avg_trend > 5:  # More than 5% average growth
            tendency = 'up'
        elif avg_trend < -5:  # More than 5% average decline
            tendency = 'down'
        else:
            tendency = 'flat'

        return {
            'tendency': tendency,
            'trend_percentage': round(avg_trend, 1),
            'historical_data': historical_revenues
        }
