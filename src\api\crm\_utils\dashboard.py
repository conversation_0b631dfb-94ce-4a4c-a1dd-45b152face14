"""
Dashboard utilities for CRM
Shared functions for dashboard calculations and data processing
"""

from django.utils import timezone
from django.db.models import Q
import requests
from decimal import Decimal
from django.conf import settings


class DashboardUtils:
    """Utility class for dashboard calculations"""
    
    @staticmethod
    def get_report_dates(request):
        """
        Get current and previous period dates based on created_at filters
        If no filters provided, defaults to current month vs previous month
        """
        created_at_after = request.GET.get("created_at_after")  # YYYY-MM-DD
        created_at_before = request.GET.get("created_at_before")  # YYYY-MM-DD

        def get_current_week():
            # current week from monday to sunday
            current_end = timezone.now()
            current_start = current_end - timezone.timedelta(
                days=current_end.weekday()
            )  # Lunes de esta semana
            current_start = current_start.replace(
                hour=0, minute=0, second=0, microsecond=0
            )

            # Previous week (7 days before)
            previous_end = current_start - timezone.timedelta(days=1)
            previous_start = previous_end - timezone.timedelta(days=6)  # 7 días atrás
            previous_start = previous_start.replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            previous_end = previous_end.replace(
                hour=23, minute=59, second=59, microsecond=999999
            )
            return current_start, current_end, previous_start, previous_end

        if created_at_after and created_at_before:
            try:
                # Parse dates from filters
                current_start = timezone.datetime.strptime(created_at_after, "%Y-%m-%d")
                current_end = timezone.datetime.strptime(created_at_before, "%Y-%m-%d")

                # Make timezone aware
                current_start = timezone.make_aware(
                    current_start.replace(hour=0, minute=0, second=0, microsecond=0)
                )
                current_end = timezone.make_aware(
                    current_end.replace(
                        hour=23, minute=59, second=59, microsecond=999999
                    )
                )

                # Calculate period duration in days
                period_duration = (current_end.date() - current_start.date()).days + 1

                # Calculate previous period (same duration, ending the day before current start)
                previous_end = current_start - timezone.timedelta(days=1)
                previous_start = previous_end - timezone.timedelta(
                    days=period_duration - 1
                )
                previous_start = previous_start.replace(
                    hour=0, minute=0, second=0, microsecond=0
                )
                previous_end = previous_end.replace(
                    hour=23, minute=59, second=59, microsecond=999999
                )

            except (ValueError, TypeError):
                # Fallback to current week if invalid format
                current_start, current_end, previous_start, previous_end = (
                    get_current_week()
                )
        else:
            # No date filters provided - default to current week vs previous week
            current_start, current_end, previous_start, previous_end = (
                get_current_week()
            )

        return {
            "current_start": current_start,
            "current_end": current_end,
            "previous_start": previous_start,
            "previous_end": previous_end,
            "period_days": (current_end.date() - current_start.date()).days + 1,
            "current_month": current_start.strftime(
                "%Y-%m"
            ),  # use current start to calculate current month
        }

    @staticmethod
    def get_queryset_excluding_filters(queryset, filterset_class, request, exclude_fields):
        """
        Get queryset, applies all filters EXCEPT specified fields
        """
        filter_params = request.GET.copy()

        if isinstance(exclude_fields, str):
            exclude_fields = [exclude_fields]

        for field in exclude_fields:
            if field == "created_at":
                # Remove date range filters
                filter_params.pop("created_at_after", None)
                filter_params.pop("created_at_before", None)
            else:
                filter_params.pop(field, None)

        # Apply remaining filters
        filterset = filterset_class(filter_params, queryset=queryset)
        return filterset.qs if filterset.is_valid() else queryset

    @staticmethod
    def calculate_percentage_change(current, previous):
        """Calculate percentage change between two values"""
        if previous == 0:
            if current > 0:
                return 100.0, "up"
            else:
                return 0.0, "flat"
        
        percentage = ((current - previous) / previous) * 100
        
        if percentage > 0:
            tendency = "up"
        elif percentage < 0:
            tendency = "down"
        else:
            tendency = "flat"
            
        return round(percentage, 1), tendency

    @staticmethod
    def get_month_names():
        """Get Spanish month names for charts"""
        return [
            "ene", "feb", "mar", "abr", "may", "jun",
            "jul", "ago", "sep", "oct", "nov", "dic"
        ]


class CurrencyConverter:
    """Utility class for currency conversion"""
    
    @staticmethod
    def usd_to_pen(amount_usd):
        """
        Convert USD to PEN using FastForex API
        Returns the amount in PEN or the original amount if conversion fails
        """
        if not amount_usd or amount_usd == 0:
            return Decimal('0.00')
            
        try:
            # FastForex API endpoint
            api_key = getattr(settings, 'FASTFOREX_API_KEY', None)
            if not api_key:
                # Fallback to a fixed rate if no API key
                return amount_usd * Decimal('3.75')  # Approximate rate
                
            url = f"https://api.fastforex.io/fetch-one?from=USD&to=PEN&api_key={api_key}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                rate = Decimal(str(data['result']['PEN']))
                return amount_usd * rate
            else:
                # Fallback rate
                return amount_usd * Decimal('3.75')
                
        except Exception:
            # Fallback rate in case of any error
            return amount_usd * Decimal('3.75')

    @staticmethod
    def convert_order_total_to_pen(order):
        """
        Convert order total to PEN based on is_international flag
        """
        if order.is_international:
            # Convert from USD to PEN
            usd_total = sum(item.effective_total_price for item in order.items.all())
            return CurrencyConverter.usd_to_pen(usd_total)
        else:
            # Already in PEN
            return sum(item.effective_total_price for item in order.items.all())


class StageNameMapper:
    """Utility class for mapping stage codes to Spanish names"""
    
    STAGE_NAMES = {
        'prospect': 'Prospecto',
        'interested': 'Interesado', 
        'to_pay': 'Por pagar',
        'sold': 'Vendido',
        'lost': 'Perdido'
    }
    
    @staticmethod
    def get_stage_name(stage_code):
        """Get Spanish name for stage code"""
        return StageNameMapper.STAGE_NAMES.get(stage_code, stage_code.title())
