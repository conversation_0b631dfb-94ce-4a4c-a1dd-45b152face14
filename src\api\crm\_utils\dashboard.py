"""
Dashboard utilities for CRM
Shared functions for dashboard calculations and data processing
"""

from django.utils import timezone
import requests
from decimal import Decimal
from django.conf import settings
from services.cache.redis import CacheManager


class DashboardUtils:
    """Utility class for dashboard calculations"""

    @staticmethod
    def get_report_dates(request):
        """
        Get current and previous period dates based on created_at filters
        If no filters provided, defaults to current month vs previous month
        """
        created_at_after = request.GET.get("created_at_after")  # YYYY-MM-DD
        created_at_before = request.GET.get("created_at_before")  # YYYY-MM-DD

        def get_current_week():
            # current week from monday to sunday
            current_end = timezone.now()
            current_start = current_end - timezone.timedelta(days=current_end.weekday())
            # Lunes de esta semana
            current_start = current_start.replace(
                hour=0, minute=0, second=0, microsecond=0
            )

            # Previous week (7 days before)
            previous_end = current_start - timezone.timedelta(days=1)
            previous_start = previous_end - timezone.timedelta(days=6)  # 7 días atrás
            previous_start = previous_start.replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            previous_end = previous_end.replace(
                hour=23, minute=59, second=59, microsecond=999999
            )
            return current_start, current_end, previous_start, previous_end

        if created_at_after and created_at_before:
            try:
                # Parse dates from filters
                current_start = timezone.datetime.strptime(created_at_after, "%Y-%m-%d")
                current_end = timezone.datetime.strptime(created_at_before, "%Y-%m-%d")

                # Make timezone aware
                current_start = timezone.make_aware(
                    current_start.replace(hour=0, minute=0, second=0, microsecond=0)
                )
                current_end = timezone.make_aware(
                    current_end.replace(
                        hour=23, minute=59, second=59, microsecond=999999
                    )
                )

                # Calculate period duration in days
                period_duration = (current_end.date() - current_start.date()).days + 1

                # Calculate previous period (same duration, ending the day before current start)
                previous_end = current_start - timezone.timedelta(days=1)
                previous_start = previous_end - timezone.timedelta(
                    days=period_duration - 1
                )
                previous_start = previous_start.replace(
                    hour=0, minute=0, second=0, microsecond=0
                )
                previous_end = previous_end.replace(
                    hour=23, minute=59, second=59, microsecond=999999
                )

            except (ValueError, TypeError):
                # Fallback to current week if invalid format
                current_start, current_end, previous_start, previous_end = (
                    get_current_week()
                )
        else:
            # No date filters provided - default to current week vs previous week
            current_start, current_end, previous_start, previous_end = (
                get_current_week()
            )

        return {
            "current_start": current_start,
            "current_end": current_end,
            "previous_start": previous_start,
            "previous_end": previous_end,
            "period_days": (current_end.date() - current_start.date()).days + 1,
            "current_month": current_start.strftime(
                "%Y-%m"
            ),  # use current start to calculate current month
        }

    @staticmethod
    def get_queryset_excluding_filters(
        queryset, filterset_class, request, exclude_fields
    ):
        """
        Get queryset, applies all filters EXCEPT specified fields
        """
        filter_params = request.GET.copy()

        if isinstance(exclude_fields, str):
            exclude_fields = [exclude_fields]

        for field in exclude_fields:
            if field == "created_at":
                # Remove date range filters
                filter_params.pop("created_at_after", None)
                filter_params.pop("created_at_before", None)
                filter_params.pop("created_at", None)
            else:
                filter_params.pop(field, None)

        # Apply remaining filters
        filterset = filterset_class(filter_params, queryset=queryset)
        return filterset.qs if filterset.is_valid() else queryset

    @staticmethod
    def calculate_percentage_change(current, previous):
        """Calculate percentage change between two values"""
        if previous == 0:
            if current > 0:
                return 100.0, "up"
            else:
                return 0.0, "flat"

        percentage = ((current - previous) / previous) * 100

        if percentage > 0:
            tendency = "up"
        elif percentage < 0:
            tendency = "down"
        else:
            tendency = "flat"

        return round(percentage, 1), tendency

    @staticmethod
    def get_month_names(full_name=False):
        """Get Spanish month names for charts"""
        return (
            [
                "ene",
                "feb",
                "mar",
                "abr",
                "may",
                "jun",
                "jul",
                "ago",
                "sep",
                "oct",
                "nov",
                "dic",
            ]
            if not full_name
            else [
                "Enero",
                "Febrero",
                "Marzo",
                "Abril",
                "Mayo",
                "Junio",
                "Julio",
                "Agosto",
                "Septiembre",
                "Octubre",
                "Noviembre",
                "Diciembre",
            ]
        )


class CurrencyConverter:
    """Utility class for currency conversion"""

    CACHE_PREFIX = "currency_converter"
    CACHE_KEY = "converter_usd_to_pen_rate"
    CACHE_TIMEOUT = 3600  # 1 hour by default

    FALLBACK_RATE = Decimal("3.75")
    cache_manager = CacheManager(prefix=CACHE_PREFIX, timeout=CACHE_TIMEOUT)

    @classmethod
    def get_conversion_rate(cls):
        """
        Get USD to PEN conversion rate with caching
        Makes only one API call and caches the result for 1 hour

        Returns:
            Decimal: USD to PEN conversion rate
        """
        # Try to get rate from cache first
        cached_rate = cls.cache_manager.get(cls.CACHE_KEY)
        if cached_rate:
            return Decimal(str(cached_rate))

        try:
            # Exchange Rate API endpoint
            api_key = getattr(settings, "EXCHANGE_RATE_API_KEY", None)
            if not api_key:
                # Cache fallback rate and return
                cls.cache_manager.set(cls.CACHE_KEY, float(cls.FALLBACK_RATE))
                return cls.FALLBACK_RATE

            url = f"https://v6.exchangerate-api.com/v6/{api_key}/pair/USD/PEN"
            response = requests.get(url, timeout=5)

            if response.status_code == 200:
                data = response.json()
                if data.get("result") == "success":
                    rate = Decimal(str(data["conversion_rate"]))
                    # Cache the successful rate
                    cls.cache_manager.set(cls.CACHE_KEY, float(rate))
                    return rate
                else:
                    # Cache fallback rate and return
                    cls.cache_manager.set(cls.CACHE_KEY, float(cls.FALLBACK_RATE))
                    return cls.FALLBACK_RATE
            else:
                # Cache fallback rate and return
                cls.cache_manager.set(cls.CACHE_KEY, float(cls.FALLBACK_RATE))
                return cls.FALLBACK_RATE

        except Exception:
            # Cache fallback rate and return
            cls.cache_manager.set(cls.CACHE_KEY, float(cls.FALLBACK_RATE))
            return cls.FALLBACK_RATE

    @classmethod
    def usd_to_pen(cls, amount_usd):
        """
        Convert USD to PEN using cached conversion rate
        Uses get_conversion_rate() to avoid multiple API calls

        Args:
            amount_usd: Amount in USD to convert

        Returns:
            Decimal: Amount in PEN
        """
        if not amount_usd or amount_usd == 0:
            return Decimal("0.00")

        # Get cached conversion rate (only makes API call if not cached)
        rate = cls.get_conversion_rate()
        return amount_usd * rate

    @classmethod
    def invalidate_cache(cls):
        """
        Invalidate the cached conversion rate
        Useful for forcing a fresh API call
        """
        cls.cache_manager.invalidate_specific(cls.CACHE_KEY)