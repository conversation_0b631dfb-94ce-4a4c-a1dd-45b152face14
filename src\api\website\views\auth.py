from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.authtoken.models import Token
from api.shared.serializers.auth import (
    TokenSerializer,
)
from api.website.serializers.auth import (
    WebsiteAuthLoginSerializer,
    WebsiteAuthRegisterSerializer,
)
from api.exceptions import InvalidCredentials
from api.mixins import SwaggerTagMixin
from core.models import User
from drf_yasg.utils import swagger_auto_schema
from api.website.tasks.notification import send_welcome_email


class WebsiteAuthViewSet(viewsets.GenericViewSet, SwaggerTagMixin):
    """
    Authentication ViewSet
    """

    queryset = User.objects.filter(deleted=False)
    serializer_class = WebsiteAuthLoginSerializer

    swagger_tags = ["Auth"]

    @swagger_auto_schema(
        methods=["POST"],
        responses={
            status.HTTP_200_OK: TokenSerializer(),
        },
    )
    @action(detail=False, methods=["POST"])
    def login(self, request, *args, **kwargs):
        """
        Login user and return auth token
        """
        serializer = self.get_serializer(
            data=request.data, context={"request": request}
        )

        try:
            serializer.is_valid(raise_exception=True)
            user = serializer.validated_data["user"]
        except Exception:
            raise InvalidCredentials(detail="Correo o contraseña incorrectos.")
        token, _ = Token.objects.get_or_create(user=user)
        return Response(TokenSerializer(token).data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        methods=["POST"],
        responses={
            status.HTTP_200_OK: TokenSerializer(),
        },
    )
    @action(detail=False, methods=["POST"])
    def register(self, request, *args, **kwargs):
        """
        Register user and return auth token
        """
        serializer = WebsiteAuthRegisterSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        user = serializer.save()
        try:
            send_welcome_email.delay(
                user_uid=user.uid,
                email=user.email,
            )
        except Exception as e:
            print(f"Error queueing the task: {str(e)}")
        token, _ = Token.objects.get_or_create(user=user)
        return Response(TokenSerializer(token).data, status=status.HTTP_200_OK)
