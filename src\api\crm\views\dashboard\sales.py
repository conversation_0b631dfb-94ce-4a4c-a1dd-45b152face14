"""
Sales Dashboard Views for CRM
Provides analytics endpoints for sales dashboard
"""

from django.db.models import Q
from django.utils import timezone
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from decimal import Decimal
from core.models import Order
from api.mixins import AuditMixin, SwaggerTagMixin
from services.cache.redis import CacheManager
from api.crm.filters.dashboard.sales import CrmDashboardSalesFilter
from api.crm.serializers.dashboard.sales import (
    CrmDashboardSalesSerializer,
    CrmDashboardSalesFilterOptionsSerializer,
)
from api.crm.serializers.order import CrmOrderSerializer
from api.crm._utils.dashboard import (
    DashboardUtils,
    CurrencyConverter,
)
from datetime import timedelta
from dateutil.relativedelta import relativedelta
from django.conf import settings


class CrmDashboardSalesViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.GenericViewSet,
):
    """
    ViewSet for Sales Dashboard Analytics
    Provides various endpoints for sales dashboard statistics and charts
    """

    model_class = Order
    # authentication_classes = [TokenAuthentication]
    # permission_classes = [IsAuthenticated]
    filterset_class = CrmDashboardSalesFilter
    swagger_tags = ["CRM Dashboard"]
    serializer_class = CrmDashboardSalesSerializer
    currency_converter = CurrencyConverter()

    def get_serializer_class(self):
        if self.action == "invalidate_cache":
            return None
        return super().get_serializer_class()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cache_manager = CacheManager("crm_dashboard_sales")
        self.cache_timeout = 60 * 5  # 5 minutes

    def initial(self, request, *args, **kwargs):
        """
        Check for force_refresh parameter before any endpoint execution
        """
        super().initial(request, *args, **kwargs)

        # Force refresh cache if requested
        if request.GET.get("force_refresh", "").lower() == "true" and not getattr(
            self, "_cache_invalidated", False
        ):
            self.cache_manager.invalidate()
            self._cache_invalidated = True

    def get_queryset(self):
        """
        Get base queryset for orders (non-deleted orders only)
        """
        return Order.objects.filter(deleted=False)

    def get_filtered_queryset(self):
        """
        Get filtered queryset based on request filters
        """
        queryset = self.get_queryset()
        filterset = self.filterset_class(self.request.GET, queryset=queryset)
        return filterset.qs if filterset.is_valid() else queryset

    def get_cache_key_params(self):
        """
        Get parameters for cache key generation
        """
        return {
            "created_at_after": self.request.GET.get("created_at_after", ""),
            "created_at_before": self.request.GET.get("created_at_before", ""),
            "stages": self.request.GET.get("stages", ""),
            "products": self.request.GET.get("products", ""),
            "sales_agent": self.request.GET.get("sales_agent", ""),
            "months": self.request.GET.get("months", "10"),
        }

    # ==== Utilities ====

    def _get_report_dates(self):
        """Get current and previous period dates"""
        return DashboardUtils.get_report_dates(self.request)

    def _get_queryset_excluding_filters(self, exclude_fields):
        """Get queryset excluding specific filters"""
        return DashboardUtils.get_queryset_excluding_filters(
            self.get_queryset(), self.filterset_class, self.request, exclude_fields
        )

    def _get_totals_sales_amount(self, orders: list[Order]):
        # Total sales amount in PEN
        total_sales_amount = Decimal("0.00")
        total_sales_amount_usd = Decimal("0.00")
        total_sales_amount_pen = Decimal("0.00")

        for order in orders:
            usd_to_pen = Decimal("0.00")
            pen = Decimal("0.00")
            if order.is_international:
                # Total sales in USD
                usd_to_pen = CurrencyConverter.usd_to_pen(order.total)
                total_sales_amount_usd += order.total
            else:
                pen = order.total
                # Total sales in PEN
                total_sales_amount_pen += order.total
            # USD + PEN
            total_sales_amount += usd_to_pen + pen

        return {
            "total_sales_amount_usd": total_sales_amount_usd,
            "total_sales_amount_pen": total_sales_amount_pen,
            "total_sales_amount": total_sales_amount,
        }

    # ==== CALCULATION FUNCTIONS ====

    def calculate_general_stats(self):
        """
        Calculate general dashboard statistics
        """
        dates = self._get_report_dates()
        filtered_queryset = self.get_filtered_queryset()

        # Total sales amount
        filtered_queryset = self.get_filtered_queryset()
        orders = filtered_queryset.filter(stage=Order.SOLD_STAGE)
        total_sales = self._get_totals_sales_amount(orders)
        total_sales_amount_usd = total_sales["total_sales_amount_usd"]
        total_sales_amount_pen = total_sales["total_sales_amount_pen"]
        total_sales_amount = total_sales["total_sales_amount"]

        # Total unique clients
        clients = filtered_queryset.values("owner").distinct().count()

        # Current period dates
        current_start = dates["current_start"]
        current_end = dates["current_end"]
        previous_start = dates["previous_start"]
        previous_end = dates["previous_end"]

        # Conversion rate calculation
        conversion_base_queryset = self._get_queryset_excluding_filters("created_at")

        # Converted orders in current period
        current_converted = (
            conversion_base_queryset.filter(
                sold_at__gte=current_start, sold_at__lte=current_end
            )
            .distinct()
            .count()
        )

        # Total opportunities in current period (any stage activity)
        current_opportunities = (
            conversion_base_queryset.filter(
                Q(prospect_at__gte=current_start, prospect_at__lte=current_end)
                | Q(interested_at__gte=current_start, interested_at__lte=current_end)
                | Q(to_pay_at__gte=current_start, to_pay_at__lte=current_end)
                | Q(sold_at__gte=current_start, sold_at__lte=current_end)
            )
            .distinct()
            .count()
        )

        # Previous period conversion for comparison
        previous_converted = conversion_base_queryset.filter(
            sold_at__gte=previous_start, sold_at__lte=previous_end
        ).count()

        previous_opportunities = (
            conversion_base_queryset.filter(
                Q(prospect_at__gte=previous_start, prospect_at__lte=previous_end)
                | Q(interested_at__gte=previous_start, interested_at__lte=previous_end)
                | Q(to_pay_at__gte=previous_start, to_pay_at__lte=previous_end)
                | Q(sold_at__gte=previous_start, sold_at__lte=previous_end)
            )
            .distinct()
            .count()
        )

        # Calculate conversion rates
        current_conversion_rate = (
            (current_converted / current_opportunities * 100)
            if current_opportunities > 0
            else 0
        )

        previous_conversion_rate = (
            (previous_converted / previous_opportunities * 100)
            if previous_opportunities > 0
            else 0
        )

        conversion_percentage, conversion_tendency = (
            DashboardUtils.calculate_percentage_change(
                current_conversion_rate, previous_conversion_rate
            )
        )

        # Sales this month
        current_month_sales = filtered_queryset.filter(
            stage=Order.SOLD_STAGE, sold_at__gte=current_start, sold_at__lte=current_end
        ).count()

        previous_month_sales = (
            self.get_queryset()
            .filter(
                stage=Order.SOLD_STAGE,
                sold_at__gte=previous_start,
                sold_at__lte=previous_end,
            )
            .count()
        )

        sales_percentage, sales_tendency = DashboardUtils.calculate_percentage_change(
            current_month_sales, previous_month_sales
        )

        return {
            # "total_sales_amount": round(float(total_sales_amount), 2),
            "total_sales_amount": {
                "pen": round(total_sales_amount_pen, 2),
                "usd": round(total_sales_amount_usd, 2),
                "total": round(total_sales_amount, 2),
                "currency_exchange_rate": CurrencyConverter.get_conversion_rate(),
            },
            "clients": clients,
            "conversion_rate": {
                "value": round(current_conversion_rate, 2),
                "percentage": round(conversion_percentage, 2),
                "tendency": conversion_tendency,
            },
            "sales_this_month": {
                "value": current_month_sales,
                "percentage": round(sales_percentage, 2),
                "tendency": sales_tendency,
            },
        }

    def calculate_conversion_funnel(self):
        """
        Calculate conversion funnel data based on cumulative progression through stages
        Shows how many orders have reached each stage during the selected period
        """
        dates = self._get_report_dates()
        current_start = dates["current_start"]
        current_end = dates["current_end"]

        # Get base queryset excluding date filters to apply stage-specific date filters
        filtered_base = self._get_queryset_excluding_filters("created_at")

        funnel_data = []

        # Define stages with their timestamp fields
        stages = [
            ("prospect", "Prospecto", "prospect_at"),
            ("interested", "Interesado", "interested_at"),
            ("to_pay", "Por pagar", "to_pay_at"),
            ("sold", "Vendido", "sold_at"),
        ]

        for stage_code, stage_name, timestamp_field in stages:
            # Count orders that reached this stage during the selected period
            stage_filter = {
                f"{timestamp_field}__gte": current_start,
                f"{timestamp_field}__lte": current_end,
                f"{timestamp_field}__isnull": False,
            }

            count = filtered_base.filter(**stage_filter).count()

            funnel_data.append(
                {"name": stage_name, "value": count, "stage": stage_code}
            )

        return funnel_data

    def calculate_conversion_by_stages(self):
        """
        Calculate conversion rates between stages using cumulative progression
        """
        dates = self._get_report_dates()
        current_start = dates["current_start"]
        current_end = dates["current_end"]

        # Get base queryset excluding date filters
        filtered_base = self._get_queryset_excluding_filters("created_at")

        # Count orders that reached each stage during the period
        prospect_count = filtered_base.filter(
            prospect_at__gte=current_start,
            prospect_at__lte=current_end,
            prospect_at__isnull=False,
        ).count()

        interested_count = filtered_base.filter(
            interested_at__gte=current_start,
            interested_at__lte=current_end,
            interested_at__isnull=False,
        ).count()

        to_pay_count = filtered_base.filter(
            to_pay_at__gte=current_start,
            to_pay_at__lte=current_end,
            to_pay_at__isnull=False,
        ).count()

        sold_count = filtered_base.filter(
            sold_at__gte=current_start, sold_at__lte=current_end, sold_at__isnull=False
        ).count()

        total_unique_orders = (
            filtered_base.filter(
                Q(prospect_at__gte=current_start, prospect_at__lte=current_end)
                | Q(interested_at__gte=current_start, interested_at__lte=current_end)
                | Q(to_pay_at__gte=current_start, to_pay_at__lte=current_end)
                | Q(sold_at__gte=current_start, sold_at__lte=current_end)
            )
            .distinct()
            .count()
        )

        # Count lost orders (current stage = lost)
        lost_count = self.get_filtered_queryset().filter(stage=Order.LOST_STAGE).count()

        # Calculate conversion percentages
        prospect_to_interested = (
            (interested_count / prospect_count * 100) if prospect_count > 0 else 0
        )
        interested_to_pay = (
            (to_pay_count / interested_count * 100) if interested_count > 0 else 0
        )
        to_pay_to_paid = (sold_count / to_pay_count * 100) if to_pay_count > 0 else 0

        # General conversion rate (prospect to sold)
        general_conversion_rate = (
            (sold_count / total_unique_orders * 100) if total_unique_orders > 0 else 0
        )

        return {
            "prospect_to_interested": {
                "percentage": round(prospect_to_interested, 1),
                "from_count": prospect_count,
                "to_count": interested_count,
            },
            "interested_to_pay": {
                "percentage": round(interested_to_pay, 1),
                "from_count": interested_count,
                "to_count": to_pay_count,
            },
            "to_pay_to_paid": {
                "percentage": round(to_pay_to_paid, 1),
                "from_count": to_pay_count,
                "to_count": sold_count,
            },
            "resume": {
                "general_conversion_rate": round(general_conversion_rate, 1),
                "lost": lost_count,
                "closed_sales": sold_count,
            },
        }

    def calculate_orders_by_month(self):
        """
        Calculate orders by month for the last 10 months
        """
        months = DashboardUtils.get_month_names()
        filtered_queryset = self.get_filtered_queryset()

        # Get last 10 months
        current_date = timezone.now()
        orders_by_month = []

        for i in range(9, -1, -1):  # Last 10 months
            month_date = current_date - timedelta(days=30 * i)
            month_start = month_date.replace(
                day=1, hour=0, minute=0, second=0, microsecond=0
            )

            # Calculate next month start
            if month_start.month == 12:
                month_end = month_start.replace(
                    year=month_start.year + 1, month=1
                ) - timedelta(seconds=1)
            else:
                month_end = month_start.replace(
                    month=month_start.month + 1
                ) - timedelta(seconds=1)

            month_orders = filtered_queryset.filter(
                created_at__gte=month_start, created_at__lte=month_end
            )

            month_data = {
                "month": months[month_start.month - 1],
                "total": month_orders.count(),
                "prospect": month_orders.filter(stage=Order.PROSPECT_STAGE).count(),
                "interested": month_orders.filter(stage=Order.INTERESTED_STAGE).count(),
                "to_pay": month_orders.filter(stage=Order.TO_PAY_STAGE).count(),
                "sold": month_orders.filter(stage=Order.SOLD_STAGE).count(),
                "lost": month_orders.filter(stage=Order.LOST_STAGE).count(),
            }

            orders_by_month.append(month_data)

        return orders_by_month

    def calculate_orders_by_stage(self):
        """
        Calculate orders by stage for pie chart
        """
        filtered_queryset = self.get_filtered_queryset()
        total_orders = filtered_queryset.count()

        if total_orders == 0:
            return []

        stages_data = []
        stages = [
            (Order.PROSPECT_STAGE, "Prospecto"),
            (Order.INTERESTED_STAGE, "Interesado"),
            (Order.TO_PAY_STAGE, "Por pagar"),
            (Order.SOLD_STAGE, "Vendido"),
            (Order.LOST_STAGE, "Perdido"),
        ]

        for stage_code, stage_name in stages:
            count = filtered_queryset.filter(stage=stage_code).count()
            percentage = (
                round((count / total_orders * 100), 1) if total_orders > 0 else 0
            )

            stages_data.append(
                {"name": stage_name, "value": percentage, "stage": stage_code}
            )

        return stages_data

    def calculate_weekly_stage_evolution(self):
        """
        Calculate weekly evolution of orders by stage for current week
        Fixed weekly metric showing progression through sales stages
        """
        # Get current week (Monday to Sunday)
        now = timezone.now()
        current_weekday = now.weekday()  # Monday = 0, Sunday = 6
        week_start = now - timedelta(days=current_weekday)
        week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)

        # Generate 7 days of the week
        weekly_evolution = []
        days_names = ["Lun", "Mar", "Mié", "Jue", "Vie", "Sáb", "Dom"]

        # Apply non-date filters
        base_queryset = self._get_queryset_excluding_filters("created_at")

        for i in range(7):
            day_date = week_start + timedelta(days=i)
            day_start = day_date.replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = day_date.replace(
                hour=23, minute=59, second=59, microsecond=999999
            )

            # Count orders by stage for this day (cumulative up to this day)
            day_data = {
                "day": days_names[i],
                "date": day_date.strftime("%Y-%m-%d"),
                "prospect": base_queryset.filter(
                    prospect_at__gte=day_start,
                    prospect_at__lte=day_end,
                    prospect_at__isnull=False,
                ).count(),
                "interested": base_queryset.filter(
                    interested_at__gte=day_start,
                    interested_at__lte=day_end,
                    interested_at__isnull=False,
                ).count(),
                "to_pay": base_queryset.filter(
                    to_pay_at__gte=day_start,
                    to_pay_at__lte=day_end,
                    to_pay_at__isnull=False,
                ).count(),
                "sold": base_queryset.filter(
                    sold_at__gte=day_start, sold_at__lte=day_end, sold_at__isnull=False
                ).count(),
                "lost": base_queryset.filter(
                    lost_at__gte=day_start, lost_at__lte=day_end, lost_at__isnull=False
                ).count(),
            }

            weekly_evolution.append(day_data)

        return weekly_evolution

    def calculate_revenue_by_month(self):
        """
        Calculate revenue by month for the last 10 months (in PEN)
        """
        months = DashboardUtils.get_month_names()
        filtered_queryset = self.get_filtered_queryset()

        # Get last 10 months
        current_date = timezone.now()
        revenue_by_month = []

        for i in range(9, -1, -1):  # Last 10 months
            month_date = current_date - timedelta(days=30 * i)
            month_start = month_date.replace(
                day=1, hour=0, minute=0, second=0, microsecond=0
            )

            # Calculate next month start
            if month_start.month == 12:
                month_end = month_start.replace(
                    year=month_start.year + 1, month=1
                ) - timedelta(seconds=1)
            else:
                month_end = month_start.replace(
                    month=month_start.month + 1
                ) - timedelta(seconds=1)

            # Get sold orders for this month
            month_orders = filtered_queryset.filter(
                stage=Order.SOLD_STAGE, sold_at__gte=month_start, sold_at__lte=month_end
            )

            # Calculate total revenue in PEN using custom_amount for sold orders
            total_revenue = 0
            for order in month_orders:
                order_revenue = 0
                for item in order.items.all():
                    if item.custom_amount is not None:
                        item_revenue = item.custom_amount * item.quantity
                    else:
                        item_revenue = item.effective_total_price

                    # Convert to PEN if international
                    if order.is_international:
                        item_revenue = CurrencyConverter.usd_to_pen(item_revenue)

                    order_revenue += item_revenue

                total_revenue += order_revenue

            revenue_by_month.append(
                {
                    "month": months[month_start.month - 1],
                    "revenue": round(float(total_revenue), 2),
                }
            )

        return revenue_by_month

    def calculate_revenue_by_sales_agent(self):
        """
        Calculate revenue by sales agent (in PEN)
        """
        filtered_queryset = self.get_filtered_queryset().filter(
            stage=Order.SOLD_STAGE, sales_agent__isnull=False
        )

        # Group by sales agent
        agents_revenue = {}
        total_revenue = 0

        for order in filtered_queryset:
            agent_uid = str(order.sales_agent.uid)
            agent_name = order.sales_agent.get_full_name() or "Sin nombre"

            # Calculate order revenue using custom_amount for sold orders
            order_revenue = 0
            for item in order.items.all():
                if item.custom_amount is not None:
                    item_revenue = item.custom_amount * item.quantity
                else:
                    item_revenue = item.effective_total_price

                # Convert to PEN if international
                if order.is_international:
                    item_revenue = CurrencyConverter.usd_to_pen(item_revenue)

                order_revenue += item_revenue

            total_revenue += order_revenue

            if agent_uid not in agents_revenue:
                agents_revenue[agent_uid] = {
                    "name": agent_name,
                    "value": 0,
                    "agent_uid": agent_uid,
                }

            agents_revenue[agent_uid]["value"] += order_revenue

        # Calculate percentages and sort by revenue
        revenue_data = []
        for agent_data in agents_revenue.values():
            percentage = (
                (agent_data["value"] / total_revenue * 100) if total_revenue > 0 else 0
            )
            revenue_data.append(
                {
                    "name": agent_data["name"],
                    "value": round(float(agent_data["value"]), 2),
                    "agent_uid": agent_data["agent_uid"],
                    "percentage": round(percentage, 1),
                }
            )

        # Sort by revenue descending
        revenue_data.sort(key=lambda x: x["value"], reverse=True)

        return revenue_data

    def calculate_top_selling_products(self):
        """
        Calculate top selling products/offerings
        """
        dates = self._get_report_dates()
        filtered_queryset = self.get_filtered_queryset().filter(stage=Order.SOLD_STAGE)

        # Group by offering
        products_data = {}
        total_revenue = 0

        for order in filtered_queryset:
            for item in order.items.all():
                offering_oid = str(item.offering.oid)
                offering_name = item.offering.name

                # Use custom_amount for sold orders (persisted prices)
                if item.custom_amount is not None:
                    item_revenue = item.custom_amount * item.quantity
                else:
                    item_revenue = item.effective_total_price

                # Convert to PEN if international
                if order.is_international:
                    item_revenue = CurrencyConverter.usd_to_pen(item_revenue)

                total_revenue += item_revenue

                if offering_oid not in products_data:
                    products_data[offering_oid] = {
                        "name": offering_name,
                        "count": 0,
                        "revenue": 0,
                        "offering_oid": offering_oid,
                    }

                products_data[offering_oid]["count"] += item.quantity
                products_data[offering_oid]["revenue"] += item_revenue

        # Calculate participation and trends based on revenue participation difference
        dates = self._get_report_dates()
        current_start = dates["current_start"]
        current_end = dates["current_end"]
        previous_start = dates["previous_start"]
        previous_end = dates["previous_end"]

        # Calculate previous period revenue for comparison
        previous_queryset = self.get_filtered_queryset().filter(
            stage=Order.SOLD_STAGE,
            sold_at__gte=previous_start,
            sold_at__lte=previous_end,
        )

        previous_products_data = {}
        previous_total_revenue = 0

        for order in previous_queryset:
            for item in order.items.all():
                offering_oid = str(item.offering.oid)

                # Use custom_amount for sold orders (persisted prices)
                if item.custom_amount is not None:
                    item_revenue = item.custom_amount * item.quantity
                else:
                    item_revenue = item.effective_total_price

                # Convert to PEN if international
                if order.is_international:
                    item_revenue = CurrencyConverter.usd_to_pen(item_revenue)

                previous_total_revenue += item_revenue

                if offering_oid not in previous_products_data:
                    previous_products_data[offering_oid] = 0

                previous_products_data[offering_oid] += item_revenue

        top_products = []
        for product_data in products_data.values():
            # Current period participation
            current_participation = (
                (product_data["revenue"] / total_revenue * 100)
                if total_revenue > 0
                else 0
            )

            # Previous period participation
            offering_oid = product_data["offering_oid"]
            previous_revenue = previous_products_data.get(offering_oid, 0)
            previous_participation = (
                (previous_revenue / previous_total_revenue * 100)
                if previous_total_revenue > 0
                else 0
            )

            # Calculate participation change
            participation_change = current_participation - previous_participation

            # Determine tendency based on participation change
            if participation_change > 1:  # More than 1% increase
                tendency = "up"
            elif participation_change < -1:  # More than 1% decrease
                tendency = "down"
            else:
                tendency = "flat"

            top_products.append(
                {
                    "name": product_data["name"],
                    "count": product_data["count"],
                    "revenue": round(float(product_data["revenue"]), 2),
                    "offering_oid": product_data["offering_oid"],
                    "participation": {
                        "percentage": round(current_participation, 1),
                        "tendency": tendency,
                        "change": round(participation_change, 1),
                    },
                }
            )

        # Sort by revenue descending
        top_products.sort(key=lambda x: x["revenue"], reverse=True)

        return top_products[:10]  # Top 10

    def calculate_current_month_performance(self):
        """
        Calculate current month performance compared to previous month
        """
        dates = self._get_report_dates()
        selected_start = dates["current_start"]

        # Reporte basado en rango seleccionado, se considera el mes de current_start como base
        # Ej: rango=2025-07-15 - 2025-07-18
        # current_start=2025-07-01, previous_start = 2025-06-01

        current_start = selected_start.replace(
            day=1, hour=0, minute=0, second=0, microsecond=0
        )
        current_end = current_start + relativedelta(months=1) - timedelta(seconds=1)

        previous_start = current_start - relativedelta(months=1)
        previous_end = current_start - timedelta(seconds=1)

        filtered_queryset = self.get_filtered_queryset()

        # Current period sales revenue
        current_orders = filtered_queryset.filter(
            stage=Order.SOLD_STAGE, sold_at__gte=current_start, sold_at__lte=current_end
        )

        total_sales = self._get_totals_sales_amount(current_orders)
        current_sales_revenue = total_sales["total_sales_amount"]

        # Previous period sales revenue
        previous_orders = self.get_queryset().filter(
            stage=Order.SOLD_STAGE,
            sold_at__gte=previous_start,
            sold_at__lte=previous_end,
        )

        total_previous_sales = self._get_totals_sales_amount(previous_orders)
        previous_sales_revenue = total_previous_sales["total_sales_amount"]

        # Orders count
        current_orders_count = filtered_queryset.filter(
            created_at__gte=current_start, created_at__lte=current_end
        ).count()

        previous_orders_count = (
            self.get_queryset()
            .filter(created_at__gte=previous_start, created_at__lte=previous_end)
            .count()
        )

        # Conversion rates
        current_converted = filtered_queryset.filter(
            sold_at__gte=current_start, sold_at__lte=current_end
        ).count()

        current_opportunities = (
            filtered_queryset.filter(
                Q(prospect_at__gte=current_start, prospect_at__lte=current_end)
                | Q(interested_at__gte=current_start, interested_at__lte=current_end)
                | Q(to_pay_at__gte=current_start, to_pay_at__lte=current_end)
                | Q(sold_at__gte=current_start, sold_at__lte=current_end)
            )
            .distinct()
            .count()
        )

        previous_converted = (
            self.get_queryset()
            .filter(sold_at__gte=previous_start, sold_at__lte=previous_end)
            .count()
        )

        previous_opportunities = (
            self.get_queryset()
            .filter(
                Q(prospect_at__gte=previous_start, prospect_at__lte=previous_end)
                | Q(interested_at__gte=previous_start, interested_at__lte=previous_end)
                | Q(to_pay_at__gte=previous_start, to_pay_at__lte=previous_end)
                | Q(sold_at__gte=previous_start, sold_at__lte=previous_end)
            )
            .distinct()
            .count()
        )

        current_conversion = (
            (current_converted / current_opportunities * 100)
            if current_opportunities > 0
            else 0
        )
        previous_conversion = (
            (previous_converted / previous_opportunities * 100)
            if previous_opportunities > 0
            else 0
        )

        # Calculate percentage changes and tendencies
        conversion_change = current_conversion - previous_conversion

        conversion_tendency = "flat"
        if conversion_change > 0:
            conversion_tendency = "up"
        elif conversion_change < 0:
            conversion_tendency = "down"

        sales_percentage, sales_tendency = DashboardUtils.calculate_percentage_change(
            current_sales_revenue, previous_sales_revenue
        )

        orders_percentage, orders_tendency = DashboardUtils.calculate_percentage_change(
            current_orders_count, previous_orders_count
        )

        # Calculate sales progress (sold orders vs total orders in current period)
        current_period_orders = filtered_queryset.filter(
            created_at__gte=current_start, created_at__lte=current_end
        )

        current_sold_orders = current_period_orders.filter(stage=Order.SOLD_STAGE)

        # Calculate total revenue from sold orders in PEN
        current_sold_revenue = 0
        for order in current_sold_orders:
            if order.is_international:
                # Usar una variable temporal para la conversión
                order_revenue_pen = CurrencyConverter.usd_to_pen(order.total)
            else:
                order_revenue_pen = order.total

            current_sold_revenue += order_revenue_pen

        return {
            "period": current_start.strftime("%B %Y"),
            "sales": {
                "current": round(float(current_sales_revenue), 2),
                "previous": round(float(previous_sales_revenue), 2),
                "percentage_change": round(sales_percentage, 1),
                "tendency": sales_tendency,
            },
            "orders": {
                "current": current_orders_count,
                "previous": previous_orders_count,
                "percentage_change": round(orders_percentage, 1),
                "tendency": orders_tendency,
            },
            "conversion": {
                "current": round(current_conversion, 2),
                "previous": round(previous_conversion, 2),
                "percentage_change": round(conversion_change, 2),
                "tendency": conversion_tendency,
            },
            "sales_progress": {
                "total_revenue": round(float(current_sold_revenue), 2),
                "target": getattr(settings, "MONTHLY_SALES_TARGET"),
            },
        }

    def get_recent_orders(self):
        """
        Get recent orders for dashboard
        """
        filtered_queryset = self.get_filtered_queryset()
        recent_orders = filtered_queryset.order_by("-created_at")[:15]
        return recent_orders

    def get_filter_options(self):
        """
        Get filter options for dashboard
        """
        return {}

    # ==== DASHBOARD ENDPOINTS ====

    @action(detail=False, methods=["GET"], url_path="summary")
    def summary(self, request):
        """
        Essential dashboard data - Fast loading
        Returns core metrics and basic visualizations
        """
        cache_key = (
            f"sales_summary_{hash(str(sorted(self.get_cache_key_params().items())))}"
        )

        # Try to get from cache first
        cached_data = self.cache_manager.get(cache_key)
        if cached_data:
            return Response(cached_data)

        try:
            # Calculate essential data only
            stats = self.calculate_general_stats()
            conversion_funnel = self.calculate_conversion_funnel()
            orders_by_stage = self.calculate_orders_by_stage()
            weekly_stage_evolution = self.calculate_weekly_stage_evolution()
            filter_options = self.get_filter_options()

            # Serialize filter options
            filter_options_serializer = CrmDashboardSalesFilterOptionsSerializer(
                filter_options
            )

            # Build response data
            summary_data = {
                "stats": stats,
                "conversion_funnel": conversion_funnel,
                "orders_by_stage": orders_by_stage,
                "weekly_stage_evolution": weekly_stage_evolution,
                "filter_options": filter_options_serializer.data,
            }

            # Cache the result
            self.cache_manager.set(
                cache_key, summary_data, timeout=self.cache_timeout
            )

            return Response(summary_data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating summary data: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["GET"], url_path="analytics")
    def analytics(self, request):
        """
        Advanced analytics - Deferred loading
        Returns conversion analysis and performance metrics
        """
        cache_key = (
            f"sales_analytics_{hash(str(sorted(self.get_cache_key_params().items())))}"
        )

        # Try to get from cache first
        cached_data = self.cache_manager.get(cache_key)
        if cached_data:
            return Response(cached_data)

        try:
            # Calculate analytics data
            conversion_by_sale_stages = self.calculate_conversion_by_stages()
            current_month_performance = self.calculate_current_month_performance()

            # Build response data
            analytics_data = {
                "conversion_by_sale_stages": conversion_by_sale_stages,
                "current_month_performance": current_month_performance,
            }

            # Cache the result
            self.cache_manager.set(
                cache_key, analytics_data, timeout=self.cache_timeout
            )

            return Response(analytics_data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating analytics data: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["GET"], url_path="products")
    def products(self, request):
        """
        Top selling products - Heavy computation, deferred loading
        Returns product performance and trends
        """
        cache_key = (
            f"sales_products_{hash(str(sorted(self.get_cache_key_params().items())))}"
        )

        # Try to get from cache first
        cached_data = self.cache_manager.get(cache_key)
        if cached_data:
            return Response(cached_data)

        try:
            # Calculate products data
            top_selling_products = self.calculate_top_selling_products()

            # Build response data
            products_data = {
                "top_selling_products": top_selling_products,
            }

            # Cache the result
            self.cache_manager.set(
                cache_key, products_data, timeout=self.cache_timeout
            )

            return Response(products_data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating products data: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["GET"], url_path="agents")
    def agents(self, request):
        """
        Revenue by sales agents - Medium computation, deferred loading
        Returns agent performance metrics
        """
        cache_key = (
            f"sales_agents_{hash(str(sorted(self.get_cache_key_params().items())))}"
        )

        # Try to get from cache first
        cached_data = self.cache_manager.get(cache_key)
        if cached_data:
            return Response(cached_data)

        try:
            # Calculate agents data
            revenue_by_sales_agent = self.calculate_revenue_by_sales_agent()

            # Build response data
            agents_data = {
                "revenue_by_sales_agent": revenue_by_sales_agent,
            }

            # Cache the result
            self.cache_manager.set(
                cache_key, agents_data, timeout=self.cache_timeout
            )

            return Response(agents_data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating agents data: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["GET"], url_path="historical")
    def historical(self, request):
        """
        Historical data - Medium computation, deferred loading
        Returns monthly trends and historical analysis
        """
        cache_key = (
            f"sales_historical_{hash(str(sorted(self.get_cache_key_params().items())))}"
        )

        # Try to get from cache first
        cached_data = self.cache_manager.get(cache_key)
        if cached_data:
            return Response(cached_data)

        try:
            # Calculate historical data
            orders_by_month = self.calculate_orders_by_month()
            revenue_by_month = self.calculate_revenue_by_month()

            # Build response data
            historical_data = {
                "orders_by_month": orders_by_month,
                "revenue_by_month": revenue_by_month,
            }

            # Cache the result
            self.cache_manager.set(
                cache_key, historical_data, timeout=self.cache_timeout
            )

            return Response(historical_data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating historical data: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["GET"], url_path="recent-orders")
    def recent_orders_endpoint(self, request):
        """
        Recent orders - Fast loading, independent data
        Returns latest orders for quick reference
        """
        cache_key = (
            f"sales_recent_orders_{hash(str(sorted(self.get_cache_key_params().items())))}"
        )

        # Try to get from cache first
        cached_data = self.cache_manager.get(cache_key)
        if cached_data:
            return Response(cached_data)

        try:
            # Get recent orders
            recent_orders = self.get_recent_orders()

            # Serialize recent orders
            recent_orders_serializer = CrmOrderSerializer(recent_orders, many=True)

            # Build response data
            orders_data = {
                "recent_orders": recent_orders_serializer.data,
            }

            # Cache the result
            self.cache_manager.set(
                cache_key, orders_data, timeout=self.cache_timeout
            )

            return Response(orders_data)

        except Exception as e:
            return Response(
                {"error": f"Error getting recent orders: {str(e)}"}, status=500
            )

    # ==== COMPLETE DASHBOARD ENDPOINT (DEPRECATED) ====

    def list(self, request):
        """
        DEPRECATED: Complete dashboard data - Use specific endpoints instead

        This endpoint returns all dashboard data in a single request, which can be slow
        for large datasets. It's maintained for backward compatibility but is not
        recommended for production use due to performance implications.

        Recommended endpoints for better performance:
        - /summary/ - Essential data (fast loading)
        - /analytics/ - Advanced analytics (deferred loading)
        - /products/ - Product analysis (heavy computation, deferred)
        - /agents/ - Agent performance (deferred loading)
        - /historical/ - Historical trends (deferred loading)
        - /recent-orders/ - Recent orders (fast loading)

        Returns comprehensive sales analytics data
        """
        cache_key = (
            f"sales_dashboard_{hash(str(sorted(self.get_cache_key_params().items())))}"
        )

        # Try to get from cache first
        cached_data = self.cache_manager.get(cache_key)

        if cached_data:
            return Response(cached_data)

        try:
            # Calculate all dashboard data
            stats = self.calculate_general_stats()
            conversion_funnel = self.calculate_conversion_funnel()
            conversion_by_sale_stages = self.calculate_conversion_by_stages()
            weekly_stage_evolution = self.calculate_weekly_stage_evolution()
            orders_by_month = self.calculate_orders_by_month()
            orders_by_stage = self.calculate_orders_by_stage()
            revenue_by_month = self.calculate_revenue_by_month()
            revenue_by_sales_agent = self.calculate_revenue_by_sales_agent()
            top_selling_products = self.calculate_top_selling_products()
            current_month_performance = self.calculate_current_month_performance()
            recent_orders = self.get_recent_orders()
            filter_options = self.get_filter_options()

            # Serialize recent orders
            recent_orders_serializer = CrmOrderSerializer(recent_orders, many=True)

            # Serialize filter options
            filter_options_serializer = CrmDashboardSalesFilterOptionsSerializer(
                filter_options
            )

            # Build response data
            dashboard_data = {
                "stats": stats,
                "conversion_funnel": conversion_funnel,
                "conversion_by_sale_stages": conversion_by_sale_stages,
                "weekly_stage_evolution": weekly_stage_evolution,
                "orders_by_month": orders_by_month,
                "orders_by_stage": orders_by_stage,
                "revenue_by_month": revenue_by_month,
                "revenue_by_sales_agent": revenue_by_sales_agent,
                "top_selling_products": top_selling_products,
                "current_month_performance": current_month_performance,
                "recent_orders": recent_orders_serializer.data,
                "filter_options": filter_options_serializer.data,
            }

            # Cache the result
            self.cache_manager.set(
                cache_key, dashboard_data, timeout=self.cache_timeout
            )

            return Response(dashboard_data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating dashboard data: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["POST"], url_path="invalidate-cache")
    def invalidate_cache(self, request):
        """
        Invalidate dashboard cache
        """
        self.cache_manager.invalidate()
        return Response(
            {
                "message": "Dashboard and currency conversion cache invalidated successfully"
            }
        )
